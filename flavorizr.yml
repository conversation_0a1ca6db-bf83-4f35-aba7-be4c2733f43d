flavors:
  dev:
    app:
      name: "<PERSON>Xfer Dev"
    android:
      applicationId: "com.nicexfer.app.dev"
    ios:
      bundleId: "com.nicexfer.app.dev"
    macos:
      bundleId: "com.nicexfer.app.dev"

  stg:
    app:
      name: "NiceXfer Stg"
    android:
      applicationId: "com.nicexfer.app.stg"
    ios:
      bundleId: "com.nicexfer.app.stg"
    macos:
      bundleId: "com.nicexfer.app.stg"

  prod:
    app:
      name: "<PERSON><PERSON><PERSON>"
    android:
      applicationId: "com.nicexfer.app"
    ios:
      bundleId: "com.nicexfer.app"
    macos:
      bundleId: "com.nicexfer.app"

ide: "vscode"

instructions:
  - assets:download
  - assets:extract
  - android:flavorizrGradle
  - android:buildGradle
  - android:androidManifest
  - flutter:flavors
  - ios:xcconfig
  - ios:buildTargets
  - ios:schema
  - ios:podfile
  - ide:config
