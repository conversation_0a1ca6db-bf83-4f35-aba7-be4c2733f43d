PODS:
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sensors_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: ff50f8405aaa0ccdc7dcfb9022ca192e8ad9688f
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  sensors_plus: 7229095999f30740798f0eeef5cd120357a8f4f2
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78

PODFILE CHECKSUM: 720cddfea670babd584635056c1cf97aa02ab1c0

COCOAPODS: 1.16.2
