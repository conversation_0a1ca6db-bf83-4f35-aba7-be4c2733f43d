import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:nicexfer_app/presentation/global_widgets/form_section_header.dart';
import '../controllers/home_controller.dart';
import '../../../../core/theme/theme_helper.dart';
import '../../../../core/controllers/navigation_controller.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Material(
      color: Colors.transparent, // 透明背景，不影响主题
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            title: Text('NiceXfer'),
            centerTitle: false,
            floating: true,
            snap: true,
            actions: [
              IconButton(
                icon: Icon(LucideIcons.scanQrCode),
                onPressed: () => Get.find<NavigationController>().changeTabIndex(2),
              ),
              IconButton(
                icon: Icon(LucideIcons.bell),
                onPressed: () => Get.find<NavigationController>().changeTabIndex(3),
              ),
            ],
          ),
        ],
        body: SingleChildScrollView(
          child: Padding(
            padding: AppTheme.paddingAll8,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 欢迎卡片
                _buildWelcomeCard(context, theme),
                SizedBox(height: AppTheme.spacingVertical24),

                // 快速操作
                _buildQuickActions(context, theme),
                SizedBox(height: AppTheme.spacingVertical24),

                // 最近活动
                _buildRecentActivity(context, theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard(BuildContext context, AppTheme theme) {
    return Container(
      width: double.infinity,
      padding: AppTheme.paddingAll20,
      decoration: theme.gradientDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '欢迎使用 NiceXfer',
            style: theme.headlineStyle,
          ),
          SizedBox(height: AppTheme.spacingVertical8),
          Text(
            '安全、快速的文件传输解决方案',
            style: theme.subtitleStyle,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, AppTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormSectionHeader(
          icon: LucideIcons.clapperboard,
          title: '快捷操作',
          iconColor: theme.primary,
          iconSize: 32,
          spacing: 16,
        ),
        SizedBox(height: AppTheme.spacingVertical16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context: context,
                icon: LucideIcons.send,
                title: '发送文件',
                subtitle: '创建发送链接',
                color: theme.primary,
                onTap: () => Get.find<NavigationController>().changeTabIndex(1),
                theme: theme,
              ),
            ),
            SizedBox(width: AppTheme.spacingHorizontal16),
            Expanded(
              child: _buildActionCard(
                context: context,
                icon: LucideIcons.boxes,
                title: '收集文件',
                subtitle: '创建收集任务',
                color: theme.secondary,
                onTap: () => Get.find<NavigationController>().changeTabIndex(2),
                theme: theme,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    required AppTheme theme,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: AppTheme.borderRadius12,
      child: Container(
        padding: AppTheme.paddingAll16,
        decoration: theme.getStatCardDecoration(color),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            SizedBox(height: AppTheme.spacingVertical8),
            Text(
              title,
              style: theme.cardTitleStyle,
            ),
            SizedBox(height: AppTheme.spacingVertical4),
            Text(
              subtitle,
              style: theme.cardSubtitleStyle,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity(BuildContext context, AppTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormSectionHeader(
          icon: LucideIcons.squareActivity,
          title: '最近活动',
          iconColor: theme.primary,
          iconSize: 32,
          spacing: 16,
        ),
        SizedBox(height: AppTheme.spacingVertical16),
        Container(
          padding: AppTheme.paddingAll16,
          decoration: theme.cardDecoration,
          child: Center(
            child: Column(
              children: [
                Icon(
                  Icons.inbox_outlined,
                  size: 48,
                  color: theme.onSurfaceVariant,
                ),
                SizedBox(height: AppTheme.spacingVertical16),
                Text('暂无活动记录', style: theme.sectionTitleStyle),
                SizedBox(height: AppTheme.spacingVertical8),
                Text(
                  '开始发送或收集文件后，活动记录将显示在这里',
                  style: theme.getTextStyle(
                    color: theme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
