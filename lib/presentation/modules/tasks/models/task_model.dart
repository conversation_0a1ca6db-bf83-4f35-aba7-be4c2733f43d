enum TaskStatus { pending, inProgress, completed, expired, cancelled }

enum TaskType { send, collect }

class TaskModel {
  final String id;
  final String title;
  final String? description;
  final TaskType type;
  final TaskStatus status;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final String createdBy;
  final List<String> fileIds;
  final String? recipientEmail;
  final String? password;
  final bool requireSubmitterInfo;
  final bool allowAnonymous;
  final List<String>? allowedFileTypes;
  final int? maxFileSize;
  final int? maxFileCount;
  final String? notificationEmail;
  final String? accessLink;

  TaskModel({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    required this.status,
    required this.createdAt,
    this.expiresAt,
    required this.createdBy,
    required this.fileIds,
    this.recipientEmail,
    this.password,
    this.requireSubmitterInfo = true,
    this.allowAnonymous = false,
    this.allowedFileTypes,
    this.maxFileSize,
    this.maxFileCount,
    this.notificationEmail,
    this.accessLink,
  });

  factory TaskModel.fromJson(Map<String, dynamic> json) {
    return TaskModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      type: TaskType.values.byName(json['type']),
      status: TaskStatus.values.byName(json['status']),
      createdAt: DateTime.parse(json['created_at']),
      expiresAt: json['expires_at'] != null ? DateTime.parse(json['expires_at']) : null,
      createdBy: json['created_by'],
      fileIds: List<String>.from(json['file_ids'] ?? []),
      recipientEmail: json['recipient_email'],
      password: json['password'],
      requireSubmitterInfo: json['require_submitter_info'] ?? true,
      allowAnonymous: json['allow_anonymous'] ?? false,
      allowedFileTypes: json['allowed_file_types'] != null ? List<String>.from(json['allowed_file_types']) : null,
      maxFileSize: json['max_file_size'],
      maxFileCount: json['max_file_count'],
      notificationEmail: json['notification_email'],
      accessLink: json['access_link'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'status': status.name,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'created_by': createdBy,
      'file_ids': fileIds,
      'recipient_email': recipientEmail,
      'password': password,
      'require_submitter_info': requireSubmitterInfo,
      'allow_anonymous': allowAnonymous,
      'allowed_file_types': allowedFileTypes,
      'max_file_size': maxFileSize,
      'max_file_count': maxFileCount,
      'notification_email': notificationEmail,
      'access_link': accessLink,
    };
  }

  // 判断任务是否过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  // 获取任务状态文本
  String get statusText {
    switch (status) {
      case TaskStatus.pending:
        return '待处理';
      case TaskStatus.inProgress:
        return '进行中';
      case TaskStatus.completed:
        return '已完成';
      case TaskStatus.expired:
        return '已过期';
      case TaskStatus.cancelled:
        return '已取消';
    }
  }

  // 获取任务类型文本
  String get typeText {
    switch (type) {
      case TaskType.send:
        return '发送';
      case TaskType.collect:
        return '收集';
    }
  }

  // 创建副本并更新属性
  TaskModel copyWith({
    String? id,
    String? title,
    String? description,
    TaskType? type,
    TaskStatus? status,
    DateTime? createdAt,
    DateTime? expiresAt,
    String? createdBy,
    List<String>? fileIds,
    String? recipientEmail,
    String? password,
    bool? requireSubmitterInfo,
    bool? allowAnonymous,
    List<String>? allowedFileTypes,
    int? maxFileSize,
    int? maxFileCount,
    String? notificationEmail,
    String? accessLink,
  }) {
    return TaskModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      createdBy: createdBy ?? this.createdBy,
      fileIds: fileIds ?? this.fileIds,
      recipientEmail: recipientEmail ?? this.recipientEmail,
      password: password ?? this.password,
      requireSubmitterInfo: requireSubmitterInfo ?? this.requireSubmitterInfo,
      allowAnonymous: allowAnonymous ?? this.allowAnonymous,
      allowedFileTypes: allowedFileTypes ?? this.allowedFileTypes,
      maxFileSize: maxFileSize ?? this.maxFileSize,
      maxFileCount: maxFileCount ?? this.maxFileCount,
      notificationEmail: notificationEmail ?? this.notificationEmail,
      accessLink: accessLink ?? this.accessLink,
    );
  }
}
