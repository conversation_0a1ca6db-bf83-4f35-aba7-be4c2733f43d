import '../models/task_model.dart';

abstract class TaskRepository {
  // 获取任务列表
  Future<List<TaskModel>> getTasks({
    int? limit,
    int? offset,
    TaskType? type,
    TaskStatus? status,
    String? createdBy,
  });

  // 根据ID获取任务
  Future<TaskModel?> getTaskById(String id);

  // 创建任务
  Future<TaskModel> createTask(TaskModel task);

  // 更新任务
  Future<TaskModel> updateTask(TaskModel task);

  // 删除任务
  Future<void> deleteTask(String id);

  // 获取用户的任务统计
  Future<Map<String, int>> getTaskStats(String userId);

  // 搜索任务
  Future<List<TaskModel>> searchTasks({
    required String query,
    TaskType? type,
    TaskStatus? status,
    String? createdBy,
  });

  // 获取即将过期的任务
  Future<List<TaskModel>> getExpiringTasks({
    required Duration within,
    String? createdBy,
  });

  // 更新任务状态
  Future<TaskModel> updateTaskStatus(String id, TaskStatus status);

  // 批量删除任务
  Future<void> deleteTasks(List<String> ids);
}

// 实现类（用于Supabase集成）
class SupabaseTaskRepository implements TaskRepository {
  // TODO: 注入Supabase客户端
  // final SupabaseClient _client;

  // SupabaseTaskRepository(this._client);

  @override
  Future<List<TaskModel>> getTasks({
    int? limit,
    int? offset,
    TaskType? type,
    TaskStatus? status,
    String? createdBy,
  }) async {
    // TODO: 实现Supabase查询逻辑
    // 临时返回空列表
    return [];
  }

  @override
  Future<TaskModel?> getTaskById(String id) async {
    // TODO: 实现Supabase查询逻辑
    return null;
  }

  @override
  Future<TaskModel> createTask(TaskModel task) async {
    // TODO: 实现Supabase插入逻辑
    return task;
  }

  @override
  Future<TaskModel> updateTask(TaskModel task) async {
    // TODO: 实现Supabase更新逻辑
    return task;
  }

  @override
  Future<void> deleteTask(String id) async {
    // TODO: 实现Supabase删除逻辑
  }

  @override
  Future<Map<String, int>> getTaskStats(String userId) async {
    // TODO: 实现Supabase统计查询逻辑
    return {
      'total': 0,
      'pending': 0,
      'inProgress': 0,
      'completed': 0,
      'expired': 0,
      'cancelled': 0,
    };
  }

  @override
  Future<List<TaskModel>> searchTasks({
    required String query,
    TaskType? type,
    TaskStatus? status,
    String? createdBy,
  }) async {
    // TODO: 实现Supabase搜索逻辑
    return [];
  }

  @override
  Future<List<TaskModel>> getExpiringTasks({
    required Duration within,
    String? createdBy,
  }) async {
    // TODO: 实现Supabase查询逻辑
    return [];
  }

  @override
  Future<TaskModel> updateTaskStatus(String id, TaskStatus status) async {
    // TODO: 实现Supabase更新逻辑
    throw UnimplementedError();
  }

  @override
  Future<void> deleteTasks(List<String> ids) async {
    // TODO: 实现Supabase批量删除逻辑
  }
}
