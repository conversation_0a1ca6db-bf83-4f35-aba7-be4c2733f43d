import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/collect_controller.dart';
import '../../../../core/theme/theme_helper.dart';
import '../../../global_widgets/form_section_header.dart';

class CollectPage extends GetView<CollectController> {
  const CollectPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Material(
      color: Colors.transparent, // 透明背景，不影响主题
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            title: Text('NiceXfer'),
            centerTitle: true,
          ),
        ],
        body: SingleChildScrollView(
          padding: AppTheme.paddingAll16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 基本信息
              _buildBasicInfoSection(context, theme),
              SizedBox(height: AppTheme.spacingVertical24),

              // 文件限制
              _buildFileRestrictionsSection(context, theme),
              SizedBox(height: AppTheme.spacingVertical24),

              // 提交设置
              _buildSubmissionSettings(context, theme),
              SizedBox(height: AppTheme.spacingVertical24),

              // 通知设置
              _buildNotificationSettings(context, theme),
              SizedBox(height: AppTheme.spacingVertical32),

              // 创建按钮
              _buildCreateButton(context, theme),
              SizedBox(height: AppTheme.spacingVertical64 * 2),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection(BuildContext context, AppTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormSectionHeader(
          icon: Icons.info_outline,
          title: '基本信息',
          subtitle: '设置收集任务的基本信息和截止时间',
        ),
        SizedBox(height: AppTheme.spacingVertical20),
        TextField(
          onChanged: controller.setTaskTitle,
          decoration: theme.getEnhancedInputDecoration(
            labelText: '任务标题 *',
            hintText: '请输入收集任务的标题',
            prefixIcon: Icon(Icons.title, color: theme.primary),
          ),
        ),
        SizedBox(height: AppTheme.spacingVertical20),
        TextField(
          onChanged: controller.setTaskDescription,
          decoration: theme.getEnhancedInputDecoration(
            labelText: '任务描述',
            hintText: '请描述需要收集的文件内容和要求',
            prefixIcon: Icon(Icons.description, color: theme.primary),
          ),
          maxLines: 3,
        ),
        SizedBox(height: AppTheme.spacingVertical20),

        // 截止时间
        InkWell(
          onTap: controller.selectDeadline,
          borderRadius: AppTheme.borderRadius12,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: theme.datePickerDecoration,
            child: Row(
              children: [
                Icon(Icons.calendar_today, color: theme.primary, size: 20),
                SizedBox(width: AppTheme.spacingHorizontal16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '截止时间',
                        style: theme.getTextStyle(
                          fontSize: AppTheme.fontSize12,
                          color: theme.primary,
                          fontWeight: AppTheme.fontWeightMedium,
                        ),
                      ),
                      SizedBox(height: AppTheme.spacingVertical4),
                      Obx(() => Text(
                            controller.deadline != null
                                ? '${controller.deadline!.year}-${controller.deadline!.month.toString().padLeft(2, '0')}-${controller.deadline!.day.toString().padLeft(2, '0')}'
                                : '选择截止时间（可选）',
                            style: theme.getTextStyle(
                              fontSize: AppTheme.fontSize14,
                              color: controller.deadline != null ? theme.onSurface : theme.onSurfaceVariant,
                              fontWeight:
                                  controller.deadline != null ? AppTheme.fontWeightMedium : AppTheme.fontWeightNormal,
                            ),
                          )),
                    ],
                  ),
                ),
                Icon(Icons.arrow_drop_down, color: theme.onSurfaceVariant),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFileRestrictionsSection(BuildContext context, AppTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormSectionHeader(
          icon: Icons.lock_outline,
          title: '文件限制',
          subtitle: '设置允许的文件类型和数量',
        ),
        SizedBox(height: AppTheme.spacingVertical12),

        // 允许的文件类型
        Text(
          '允许的文件类型',
          style: theme.getTextStyle(
            fontSize: 14,
            fontWeight: AppTheme.fontWeightMedium,
          ),
        ),
        SizedBox(height: AppTheme.spacingVertical8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: controller.fileTypeOptions.map((option) {
            return Obx(() => FilterChip(
                  label: Text(option['label']!),
                  selected: controller.allowedFileTypes.contains(option['value']),
                  onSelected: (selected) {
                    if (selected) {
                      controller.addFileType(option['value']!);
                    } else {
                      controller.removeFileType(option['value']!);
                    }
                  },
                ));
          }).toList(),
        ),
        SizedBox(height: AppTheme.spacingVertical16),

        // 文件大小限制
        Row(
          children: [
            const Expanded(
              child: Text('最大文件大小'),
            ),
            Obx(() => DropdownButton<int>(
                  value: controller.maxFileSize,
                  items: const [
                    DropdownMenuItem(value: 10, child: Text('10 MB')),
                    DropdownMenuItem(value: 50, child: Text('50 MB')),
                    DropdownMenuItem(value: 100, child: Text('100 MB')),
                    DropdownMenuItem(value: 500, child: Text('500 MB')),
                    DropdownMenuItem(value: 1000, child: Text('1 GB')),
                  ],
                  onChanged: (value) => controller.setMaxFileSize(value!),
                )),
          ],
        ),
        SizedBox(height: AppTheme.spacingVertical16),

        // 文件数量限制
        Row(
          children: [
            const Expanded(
              child: Text('最大文件数量'),
            ),
            Obx(() => DropdownButton<int>(
                  value: controller.maxFileCount,
                  items: const [
                    DropdownMenuItem(value: 1, child: Text('1 个')),
                    DropdownMenuItem(value: 5, child: Text('5 个')),
                    DropdownMenuItem(value: 10, child: Text('10 个')),
                    DropdownMenuItem(value: 20, child: Text('20 个')),
                    DropdownMenuItem(value: 50, child: Text('50 个')),
                  ],
                  onChanged: (value) => controller.setMaxFileCount(value!),
                )),
          ],
        ),
      ],
    );
  }

  Widget _buildSubmissionSettings(BuildContext context, AppTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormSectionHeader(
          icon: Icons.send,
          title: '提交设置',
        ),
        SizedBox(height: AppTheme.spacingVertical12),

        // 要求提交者信息
        Obx(() => SwitchListTile(
              title: const Text('要求提交者信息'),
              subtitle: const Text('提交者需要填写姓名和邮箱'),
              value: controller.requireSubmitterInfo,
              onChanged: (_) => controller.toggleRequireSubmitterInfo(),
              contentPadding: AppTheme.paddingZero,
            )),

        // 允许匿名提交
        Obx(() => SwitchListTile(
              title: const Text('允许匿名提交'),
              subtitle: const Text('提交者可以选择匿名提交文件'),
              value: controller.allowAnonymous,
              onChanged: (_) => controller.toggleAllowAnonymous(),
              contentPadding: AppTheme.paddingZero,
            )),
      ],
    );
  }

  Widget _buildNotificationSettings(BuildContext context, AppTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormSectionHeader(
          icon: Icons.notifications_active_outlined,
          title: '通知设置',
          subtitle: '设置接收文件提交通知的邮箱',
        ),
        SizedBox(height: AppTheme.spacingVertical12),
        TextField(
          onChanged: controller.setNotificationEmail,
          decoration: theme.getInputDecoration(
            labelText: '通知邮箱 *',
            hintText: '接收文件提交通知的邮箱地址',
            prefixIcon: Icon(Icons.email_outlined, color: theme.onSurfaceVariant),
          ),
          keyboardType: TextInputType.emailAddress,
        ),
      ],
    );
  }

  Widget _buildCreateButton(BuildContext context, AppTheme theme) {
    return Obx(() => SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: controller.isCreating ? null : controller.createCollectTask,
            style: theme.enhancedPrimaryButtonStyle,
            child: controller.isCreating
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(theme.onPrimary),
                        ),
                      ),
                      SizedBox(width: AppTheme.spacingHorizontal12),
                      Text(
                        '创建中...',
                        style: theme.getTextStyle(
                          fontSize: AppTheme.fontSize16,
                          fontWeight: AppTheme.fontWeightSemiBold,
                          color: theme.onPrimary,
                        ),
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_task,
                        size: 20,
                      ),
                      SizedBox(width: AppTheme.spacingHorizontal8),
                      Text(
                        '创建收集任务',
                        style: theme.getTextStyle(
                          fontSize: AppTheme.fontSize16,
                          fontWeight: AppTheme.fontWeightSemiBold,
                          color: theme.onPrimary,
                        ),
                      ),
                    ],
                  ),
          ),
        ));
  }
}
