import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CollectController extends GetxController {
  // 收集任务状态
  final _isCreating = false.obs;
  bool get isCreating => _isCreating.value;

  // 任务标题
  final _taskTitle = ''.obs;
  String get taskTitle => _taskTitle.value;

  // 任务描述
  final _taskDescription = ''.obs;
  String get taskDescription => _taskDescription.value;

  // 允许的文件类型
  final _allowedFileTypes = <String>[].obs;
  List<String> get allowedFileTypes => _allowedFileTypes;

  // 最大文件大小（MB）
  final _maxFileSize = 100.obs;
  int get maxFileSize => _maxFileSize.value;

  // 最大文件数量
  final _maxFileCount = 10.obs;
  int get maxFileCount => _maxFileCount.value;

  // 截止时间
  final _deadline = Rxn<DateTime>();
  DateTime? get deadline => _deadline.value;

  // 是否需要提交者信息
  final _requireSubmitterInfo = true.obs;
  bool get requireSubmitterInfo => _requireSubmitterInfo.value;

  // 是否允许匿名提交
  final _allowAnonymous = false.obs;
  bool get allowAnonymous => _allowAnonymous.value;

  // 通知邮箱
  final _notificationEmail = ''.obs;
  String get notificationEmail => _notificationEmail.value;

  // 常用文件类型选项
  final List<Map<String, String>> fileTypeOptions = [
    {'label': '图片', 'value': 'image/*'},
    {'label': '文档', 'value': '.pdf,.doc,.docx,.txt'},
    {'label': '表格', 'value': '.xls,.xlsx,.csv'},
    {'label': '演示文稿', 'value': '.ppt,.pptx'},
    {'label': '压缩包', 'value': '.zip,.rar,.7z'},
    {'label': '音频', 'value': 'audio/*'},
    {'label': '视频', 'value': 'video/*'},
  ];

  // 设置任务标题
  void setTaskTitle(String title) {
    _taskTitle.value = title;
  }

  // 设置任务描述
  void setTaskDescription(String description) {
    _taskDescription.value = description;
  }

  // 添加文件类型
  void addFileType(String fileType) {
    if (!_allowedFileTypes.contains(fileType)) {
      _allowedFileTypes.add(fileType);
    }
  }

  // 移除文件类型
  void removeFileType(String fileType) {
    _allowedFileTypes.remove(fileType);
  }

  // 设置最大文件大小
  void setMaxFileSize(int size) {
    _maxFileSize.value = size;
  }

  // 设置最大文件数量
  void setMaxFileCount(int count) {
    _maxFileCount.value = count;
  }

  // 设置截止时间
  void setDeadline(DateTime? date) {
    _deadline.value = date;
  }

  // 切换提交者信息要求
  void toggleRequireSubmitterInfo() {
    _requireSubmitterInfo.value = !_requireSubmitterInfo.value;
  }

  // 切换匿名提交
  void toggleAllowAnonymous() {
    _allowAnonymous.value = !_allowAnonymous.value;
  }

  // 设置通知邮箱
  void setNotificationEmail(String email) {
    _notificationEmail.value = email;
  }

  // 创建收集任务
  void createCollectTask() {
    if (_taskTitle.isEmpty) {
      Get.snackbar('错误', '请输入任务标题');
      return;
    }

    if (_notificationEmail.isEmpty) {
      Get.snackbar('错误', '请输入通知邮箱');
      return;
    }

    _isCreating.value = true;

    // TODO: 实现收集任务创建逻辑
    Future.delayed(const Duration(seconds: 2), () {
      _isCreating.value = false;
      Get.snackbar('成功', '收集任务已创建');
      _resetForm();
    });
  }

  // 重置表单
  void _resetForm() {
    _taskTitle.value = '';
    _taskDescription.value = '';
    _allowedFileTypes.clear();
    _maxFileSize.value = 100;
    _maxFileCount.value = 10;
    _deadline.value = null;
    _requireSubmitterInfo.value = true;
    _allowAnonymous.value = false;
    _notificationEmail.value = '';
  }

  // 选择截止日期
  void selectDeadline() async {
    final DateTime? picked = await Get.dialog<DateTime>(
      DatePickerDialog(
        initialDate: DateTime.now().add(const Duration(days: 7)),
        firstDate: DateTime.now(),
        lastDate: DateTime.now().add(const Duration(days: 365)),
      ),
    );

    if (picked != null) {
      setDeadline(picked);
    }
  }
}
