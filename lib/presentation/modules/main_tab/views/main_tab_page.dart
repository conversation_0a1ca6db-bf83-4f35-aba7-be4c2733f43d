import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/controllers/navigation_controller.dart';
import '../../../global_widgets/layouts/main_layout.dart';
import '../../home/<USER>/home_page.dart';
import '../../send/views/send_page.dart';
import '../../collect/views/collect_page.dart';
import '../../records/views/records_page.dart';
import '../../files/views/files_page.dart';

class MainTabPage extends GetView<NavigationController> {
  const MainTabPage({super.key});

  // 页面列表
  final List<Widget> _pages = const [
    HomePage(),
    SendPage(),
    CollectPage(),
    RecordsPage(),
    FilesPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      child: Obx(() => IndexedStack(
            index: controller.currentIndex,
            children: _pages,
          )),
    );
  }
}
