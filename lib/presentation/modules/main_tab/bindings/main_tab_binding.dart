import 'package:get/get.dart';

import '../../home/<USER>/home_controller.dart';
import '../../send/controllers/send_controller.dart';
import '../../collect/controllers/collect_controller.dart';
import '../../records/controllers/records_controller.dart';
import '../../files/controllers/files_controller.dart';
import '../../files/controllers/search_field_controller.dart';

class MainTabBinding extends Bindings {
  @override
  void dependencies() {
    // 注册所有底部导航页面的控制器
    Get.lazyPut<HomeController>(() => HomeController());
    Get.lazyPut<SendController>(() => SendController());
    Get.lazyPut<CollectController>(() => CollectController());
    Get.lazyPut<RecordsController>(() => RecordsController());
    Get.lazyPut<FilesController>(() => FilesController());

    // 注册全局共享的控制器
    Get.lazyPut<SearchFieldController>(() => SearchFieldController());
  }
}
