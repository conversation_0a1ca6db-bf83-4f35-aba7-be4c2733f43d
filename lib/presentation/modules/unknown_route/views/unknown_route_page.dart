import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/theme/theme_helper.dart';

import '../controllers/unknown_route_controller.dart';

class UnknownRoutePage extends GetView<UnknownRouteController> {
  const UnknownRoutePage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Scaffold(
      backgroundColor: theme.surface,
      appBar: AppBar(
        title: const Text('页面未找到'),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: AppTheme.paddingAll24,
          child: isLandscape ? _buildLandscapeLayout(context, theme) : _buildPortraitLayout(context, theme),
        ),
      ),
    );
  }

  /// 构建竖屏布局
  Widget _buildPortraitLayout(BuildContext context, AppTheme theme) {
    return Column(
      children: [
        // 顶部空间
        const Spacer(flex: 2),

        // 主要内容区域
        _buildMainContent(context, theme),

        // 底部空间和按钮
        const Spacer(flex: 1),
        _buildActionButtons(context, theme),
        SizedBox(height: AppTheme.spacingVertical32),
      ],
    );
  }

  /// 构建横屏布局
  Widget _buildLandscapeLayout(BuildContext context, AppTheme theme) {
    return Row(
      children: [
        // 左侧内容区域
        Expanded(
          flex: 2,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildMainContent(context, theme),
            ],
          ),
        ),
        SizedBox(width: AppTheme.spacingHorizontal32),
        // 右侧按钮区域
        Expanded(
          flex: 3,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildActionButtons(context, theme),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建主要内容区域
  Widget _buildMainContent(BuildContext context, AppTheme theme) {
    return Column(
      children: [
        // 404 插图容器
        _buildIllustrationContainer(theme),
        SizedBox(height: AppTheme.spacingVertical32),

        // 标题和描述
        _buildTextContent(theme, context),
      ],
    );
  }

  /// 构建插图容器
  Widget _buildIllustrationContainer(AppTheme theme) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
        final size = isLandscape ? 150.0 : 200.0;

        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: theme.primaryContainer.withValues(alpha: 0.1),
            shape: BoxShape.circle,
            border: Border.all(
              color: theme.primaryContainer.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 背景装饰圆圈
              Container(
                width: size * 0.6,
                height: size * 0.6,
                decoration: BoxDecoration(
                  color: theme.primaryContainer.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
              ),
              // 主图标
              Container(
                width: size * 0.4,
                height: size * 0.4,
                decoration: BoxDecoration(
                  color: theme.primaryContainer,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.search_off_rounded,
                  size: size * 0.2,
                  color: theme.primary,
                ),
              ),
              // 404 文字
              Positioned(
                bottom: size * 0.1,
                child: Container(
                  padding: AppTheme.paddingSymmetricHorizontal20Vertical12,
                  decoration: BoxDecoration(
                    color: theme.errorContainer,
                    borderRadius: AppTheme.borderRadius16,
                    boxShadow: [
                      BoxShadow(
                        color: theme.shadow.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    '404',
                    style: TextStyle(
                      fontSize: AppTheme.fontSize16,
                      fontWeight: AppTheme.fontWeightBold,
                      color: theme.error,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建文本内容
  Widget _buildTextContent(AppTheme theme, BuildContext context) {
    final isLandscape = AppTheme.isLandscape(context);

    return Column(
      children: [
        Text(
          '页面走丢了',
          style: TextStyle(
            fontSize: isLandscape ? AppTheme.fontSize20 : AppTheme.fontSize24,
            fontWeight: AppTheme.fontWeightBold,
            color: theme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: isLandscape ? AppTheme.spacingVertical8 : AppTheme.spacingVertical12),
        Text(
          '抱歉，您访问的页面不存在或已被移除',
          style: TextStyle(
            fontSize: isLandscape ? AppTheme.fontSize14 : AppTheme.fontSize16,
            color: theme.onSurfaceVariant,
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: isLandscape ? AppTheme.spacingVertical4 : AppTheme.spacingVertical8),
        Container(
          padding: AppTheme.paddingSymmetricHorizontal20Vertical12,
          decoration: BoxDecoration(
            color: theme.surfaceContainer,
            borderRadius: AppTheme.borderRadius12,
            border: Border.all(
              color: theme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 16,
                color: theme.primary,
              ),
              SizedBox(width: AppTheme.spacingHorizontal8),
              Text(
                '请检查网址是否正确',
                style: TextStyle(
                  fontSize: AppTheme.fontSize14,
                  color: theme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context, AppTheme theme) {
    return Column(
      children: [
        // 主要操作按钮
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => Get.offAllNamed('/'),
            icon: const Icon(Icons.home_outlined),
            label: const Text('返回首页'),
            style: theme.enhancedPrimaryButtonStyle,
          ),
        ),
        SizedBox(height: AppTheme.spacingVertical12),

        // 次要操作按钮
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => Get.back(),
                icon: const Icon(Icons.arrow_back_outlined),
                label: const Text('返回上页'),
                style: theme.outlinedButtonStyle,
              ),
            ),
            SizedBox(width: AppTheme.spacingHorizontal12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _showHelpDialog(context, theme),
                icon: const Icon(Icons.help_outline),
                label: const Text('获取帮助'),
                style: theme.outlinedButtonStyle,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 显示帮助对话框
  void _showHelpDialog(BuildContext context, AppTheme theme) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.help_outline,
              color: theme.primary,
            ),
            SizedBox(width: AppTheme.spacingHorizontal8),
            const Text('需要帮助？'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHelpItem(
              icon: Icons.refresh,
              title: '刷新页面',
              description: '尝试重新加载当前页面',
              theme: theme,
            ),
            SizedBox(height: AppTheme.spacingVertical12),
            _buildHelpItem(
              icon: Icons.link,
              title: '检查链接',
              description: '确认网址拼写是否正确',
              theme: theme,
            ),
            SizedBox(height: AppTheme.spacingVertical12),
            _buildHelpItem(
              icon: Icons.contact_support,
              title: '联系支持',
              description: '如问题持续存在，请联系技术支持',
              theme: theme,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  /// 构建帮助项目
  Widget _buildHelpItem({
    required IconData icon,
    required String title,
    required String description,
    required AppTheme theme,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.primaryContainer.withValues(alpha: 0.3),
            borderRadius: AppTheme.borderRadius8,
          ),
          child: Icon(
            icon,
            size: 16,
            color: theme.primary,
          ),
        ),
        SizedBox(width: AppTheme.spacingHorizontal12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: AppTheme.fontSize14,
                  fontWeight: AppTheme.fontWeightMedium,
                  color: theme.onSurface,
                ),
              ),
              SizedBox(height: AppTheme.spacingVertical4),
              Text(
                description,
                style: TextStyle(
                  fontSize: AppTheme.fontSize12,
                  color: theme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
