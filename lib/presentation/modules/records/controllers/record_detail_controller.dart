import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/record_model.dart';

class RecordDetailController extends GetxController {
  /// 当前记录
  final record = Rxn<RecordModel>();

  /// 视图模式
  final viewMode = 'list'.obs; // 'list', 'grid'

  /// 是否正在加载
  final isLoading = false.obs;

  /// 记录ID
  String? recordId;

  @override
  void onInit() {
    super.onInit();
    recordId = Get.parameters['recordId'];
    if (recordId != null) {
      loadRecord(recordId!);
    }
  }

  /// 加载记录详情
  Future<void> loadRecord(String id) async {
    try {
      isLoading.value = true;
      
      // 模拟加载数据
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 创建模拟数据
      record.value = _createMockRecord(id);
      
    } catch (e) {
      Get.snackbar('错误', '加载记录失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 切换视图模式
  void toggleViewMode() {
    viewMode.value = viewMode.value == 'list' ? 'grid' : 'list';
  }

  /// 预览文件
  void previewFile(FileInfo file) {
    // TODO: 实现文件预览功能
    Get.snackbar('预览', '预览文件: ${file.name}');
  }

  /// 下载文件
  void downloadFile(FileInfo file) {
    // TODO: 实现文件下载功能
    Get.snackbar('下载', '开始下载: ${file.name}');
  }

  /// 分享文件
  void shareFile(FileInfo file) {
    // TODO: 实现文件分享功能
    Get.snackbar('分享', '分享文件: ${file.name}');
  }

  /// 下载全部文件
  void downloadAllFiles() {
    final currentRecord = record.value;
    if (currentRecord == null) return;

    // TODO: 实现批量下载功能
    Get.snackbar('下载', '开始下载全部 ${currentRecord.files.length} 个文件');
  }

  /// 分享记录
  void shareRecord() {
    final currentRecord = record.value;
    if (currentRecord == null) return;

    // TODO: 实现记录分享功能
    Get.snackbar('分享', '分享记录: ${currentRecord.title}');
  }

  /// 删除记录
  void deleteRecord() {
    final currentRecord = record.value;
    if (currentRecord == null) return;

    Get.dialog(
      AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除记录"${currentRecord.title}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _performDelete();
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// 执行删除操作
  void _performDelete() {
    // TODO: 实现删除功能
    Get.snackbar('删除', '记录已删除');
    Get.back(); // 返回上一页
  }

  /// 创建模拟记录数据
  RecordModel _createMockRecord(String id) {
    return RecordModel(
      id: id,
      type: RecordType.sent,
      status: RecordStatus.completed,
      title: '易家千纸鹤',
      description: '客老的动画电影都在这里，原画级别的清晰度，虽然大小很大，不过用文敢取就很快就能下完，然...',
      files: [
        FileInfo(
          id: '1',
          name: '宫崎骏电影',
          size: 8467251200, // 7.89 GB
          type: 'folder',
          uploadTime: DateTime.now().subtract(const Duration(days: 3)),
          downloadCount: 12,
        ),
        FileInfo(
          id: '2',
          name: '悬崖上的金鱼姬.mkv',
          size: 3704897536, // 3.45 GB
          type: 'mkv',
          uploadTime: DateTime.now().subtract(const Duration(days: 3)),
          downloadCount: 8,
        ),
        FileInfo(
          id: '3',
          name: '浅谈《设计心理学》.ppt',
          size: 3198976, // 3.05 MB
          type: 'ppt',
          uploadTime: DateTime.now().subtract(const Duration(days: 3)),
          downloadCount: 5,
        ),
        FileInfo(
          id: '4',
          name: '千里江山图扫描件.jpg',
          size: 132120576, // 125.28 MB
          type: 'jpg',
          uploadTime: DateTime.now().subtract(const Duration(days: 3)),
          downloadCount: 15,
        ),
        FileInfo(
          id: '5',
          name: '微信图片_20190618105310.jpg',
          size: 3637248, // 3.47 MB
          type: 'jpg',
          uploadTime: DateTime.now().subtract(const Duration(days: 3)),
          downloadCount: 3,
        ),
        FileInfo(
          id: '6',
          name: '设计心理学1-4册.txt',
          size: 4288512, // 4.09 MB
          type: 'txt',
          uploadTime: DateTime.now().subtract(const Duration(days: 3)),
          downloadCount: 7,
        ),
      ],
      sender: UserInfo(
        id: 'user1',
        name: '易家千纸鹤',
        avatar: null,
        email: '<EMAIL>',
      ),
      recipients: [
        UserInfo(
          id: 'user2',
          name: '接收者1',
          avatar: null,
        ),
        UserInfo(
          id: 'user3',
          name: '接收者2',
          avatar: null,
        ),
        UserInfo(
          id: 'user4',
          name: '接收者3',
          avatar: null,
        ),
        UserInfo(
          id: 'user5',
          name: '接收者4',
          avatar: null,
        ),
        UserInfo(
          id: 'user6',
          name: '接收者5',
          avatar: null,
        ),
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 3, hours: 23, minutes: 15, seconds: 14)),
      expiresAt: DateTime.now().add(const Duration(days: 7)),
      totalDownloads: 50,
      totalViews: 120,
    );
  }
}
