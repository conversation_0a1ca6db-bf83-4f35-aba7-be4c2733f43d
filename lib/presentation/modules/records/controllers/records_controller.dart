import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/record_model.dart';
import '../../files/controllers/search_field_controller.dart';

class RecordsController extends GetxController with GetSingleTickerProviderStateMixin {
  late TabController tabController;

  // 当前选中的Tab
  final currentTabIndex = 0.obs;

  // 搜索关键词
  final searchKeyword = ''.obs;

  // 筛选状态
  final selectedStatus = Rxn<RecordStatus>();

  // 搜索框展开状态
  final isSearchExpanded = false.obs;

  // 数据状态
  final isLoading = false.obs;
  final isRefreshing = false.obs;
  final hasMore = true.obs;

  // 各Tab的记录数据
  final sentRecords = <RecordModel>[].obs;
  final receivedRecords = <RecordModel>[].obs;
  final collectedRecords = <RecordModel>[].obs;
  final collectingRecords = <RecordModel>[].obs;

  // 各Tab的筛选缓存数据（用于性能优化）
  final filteredSentRecords = <RecordModel>[].obs;
  final filteredReceivedRecords = <RecordModel>[].obs;
  final filteredCollectedRecords = <RecordModel>[].obs;
  final filteredCollectingRecords = <RecordModel>[].obs;

  // 分页参数
  final pageSize = 20;
  final sentPage = 1.obs;
  final receivedPage = 1.obs;
  final collectedPage = 1.obs;
  final collectingPage = 1.obs;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 4, vsync: this);
    tabController.addListener(_onTabChanged);

    // 监听筛选条件变化，自动更新缓存
    ever(selectedStatus, (_) => _updateAllFilteredRecords());
    ever(searchKeyword, (_) => _updateAllFilteredRecords());

    // 初始加载第一个Tab的数据
    loadRecords(RecordType.sent);
  }

  @override
  void onClose() {
    tabController.removeListener(_onTabChanged);
    tabController.dispose();
    super.onClose();
  }

  /// Tab切换监听
  void _onTabChanged() {
    if (tabController.indexIsChanging) return;

    final newIndex = tabController.index;
    currentTabIndex.value = newIndex;

    // 根据Tab索引加载对应数据
    final recordType = RecordType.values[newIndex];
    final records = getRecordsByType(recordType);

    // 如果当前Tab没有数据，则加载
    if (records.isEmpty && !isLoading.value) {
      loadRecords(recordType);
    }
  }

  /// 手动切换Tab
  void changeTab(int index) {
    if (index < 0 || index >= RecordType.values.length) return;

    // 同步 TabController 和响应式变量
    tabController.animateTo(index);
    currentTabIndex.value = index;

    // 根据Tab索引加载对应数据
    final recordType = RecordType.values[index];
    final records = getRecordsByType(recordType);

    // 如果当前Tab没有数据，则加载
    if (records.isEmpty && !isLoading.value) {
      loadRecords(recordType);
    }
  }

  /// 根据类型获取记录列表
  List<RecordModel> getRecordsByType(RecordType type) {
    switch (type) {
      case RecordType.sent:
        return sentRecords;
      case RecordType.received:
        return receivedRecords;
      case RecordType.collected:
        return collectedRecords;
      case RecordType.collecting:
        return collectingRecords;
    }
  }

  /// 根据Tab索引获取记录（用于TabBarView中避免预加载问题）
  /// 直接返回缓存的筛选结果，避免重复计算提升性能
  List<RecordModel> getRecordsByTabIndex(int tabIndex) {
    if (tabIndex < 0 || tabIndex >= RecordType.values.length) {
      return [];
    }
    final type = RecordType.values[tabIndex];
    return _getFilteredRecordsByType(type);
  }

  /// 获取筛选后的记录
  List<RecordModel> getFilteredRecords(List<RecordModel> records) {
    var filtered = records;

    // 状态筛选
    if (selectedStatus.value != null) {
      filtered = filtered.where((record) => record.status == selectedStatus.value).toList();
    }

    // 关键词搜索
    if (searchKeyword.value.isNotEmpty) {
      final keyword = searchKeyword.value.toLowerCase();
      filtered = filtered.where((record) {
        return record.title.toLowerCase().contains(keyword) ||
            record.description?.toLowerCase().contains(keyword) == true ||
            record.files.any((file) => file.name.toLowerCase().contains(keyword));
      }).toList();
    }

    return filtered;
  }

  /// 加载记录数据
  Future<void> loadRecords(RecordType type, {bool isRefresh = false}) async {
    if (isLoading.value && !isRefresh) return;

    try {
      if (isRefresh) {
        isRefreshing.value = true;
        _resetPage(type);
      } else {
        isLoading.value = true;
      }

      final page = _getCurrentPage(type);
      final records = await _fetchRecordsFromAPI(type, page);

      if (isRefresh) {
        _setRecords(type, records);
      } else {
        _addRecords(type, records);
      }

      // 更新分页状态
      hasMore.value = records.length >= pageSize;
      _incrementPage(type);
    } catch (e) {
      Get.snackbar('错误', '加载数据失败: $e');
    } finally {
      isLoading.value = false;
      isRefreshing.value = false;
    }
  }

  /// 模拟API请求
  Future<List<RecordModel>> _fetchRecordsFromAPI(RecordType type, int page) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 800));

    // 模拟数据
    return _generateMockData(type, page);
  }

  /// 生成模拟数据
  List<RecordModel> _generateMockData(RecordType type, int page) {
    final List<RecordModel> mockData = [];
    final now = DateTime.now();
    final mockTags = ['工作', '学习', '生活', '重要', '紧急', '分享'];
    final mockEmojis = ['👍', '❤️', '😊', '🎉', '👏'];

    for (int i = 0; i < pageSize; i++) {
      final index = (page - 1) * pageSize + i;

      // 生成社交互动数据
      final social = SocialInteraction(
        likes: index % 20,
        thanks: index % 15,
        tags: index % 3 == 0 ? [mockTags[index % mockTags.length]] : [],
        transferChain: index % 4 == 0
            ? [
                UserInfo(id: 'userA_$index', name: '用户A'),
                UserInfo(id: 'userB_$index', name: '用户B'),
                UserInfo(id: 'userC_$index', name: '用户C'),
              ]
            : [],
        reactions: index % 5 == 0 ? {mockEmojis[index % mockEmojis.length]: (index % 5) + 1} : {},
        badges: index % 6 == 0 ? ['好友', '同事'] : [],
        lastInteractionAt: index % 2 == 0 ? now.subtract(Duration(minutes: index * 10)) : null,
      );

      mockData.add(RecordModel(
        id: '${type.name}_$index',
        type: type,
        status: RecordStatus.values[index % RecordStatus.values.length],
        title: '${type.displayName}任务 ${index + 1}',
        description: '这是一个${type.displayName}的示例任务描述',
        files: [
          FileInfo(
            id: 'file_${index}_1',
            name: '文档${index + 1}.pdf',
            size: 1024 * 1024 * (index % 10 + 1),
            type: 'pdf',
            uploadTime: now.subtract(Duration(hours: index)),
            downloadCount: index * 3,
          ),
        ],
        sender: type == RecordType.received || type == RecordType.collecting
            ? UserInfo(id: 'user_$index', name: '用户${index + 1}')
            : null,
        recipients: type == RecordType.sent || type == RecordType.collected
            ? [UserInfo(id: 'recipient_$index', name: '接收者${index + 1}')]
            : [],
        createdAt: now.subtract(Duration(hours: index)),
        expiresAt: now.add(Duration(days: 7 - (index % 7))),
        shareUrl: 'https://example.com/share/${type.name}_$index',
        totalDownloads: index * 2,
        totalViews: index * 5,
        social: social,
      ));
    }

    return mockData;
  }

  /// 获取当前页码
  int _getCurrentPage(RecordType type) {
    switch (type) {
      case RecordType.sent:
        return sentPage.value;
      case RecordType.received:
        return receivedPage.value;
      case RecordType.collected:
        return collectedPage.value;
      case RecordType.collecting:
        return collectingPage.value;
    }
  }

  /// 重置页码
  void _resetPage(RecordType type) {
    switch (type) {
      case RecordType.sent:
        sentPage.value = 1;
        break;
      case RecordType.received:
        receivedPage.value = 1;
        break;
      case RecordType.collected:
        collectedPage.value = 1;
        break;
      case RecordType.collecting:
        collectingPage.value = 1;
        break;
    }
  }

  /// 增加页码
  void _incrementPage(RecordType type) {
    switch (type) {
      case RecordType.sent:
        sentPage.value++;
        break;
      case RecordType.received:
        receivedPage.value++;
        break;
      case RecordType.collected:
        collectedPage.value++;
        break;
      case RecordType.collecting:
        collectingPage.value++;
        break;
    }
  }

  /// 设置记录（刷新时使用）
  void _setRecords(RecordType type, List<RecordModel> records) {
    switch (type) {
      case RecordType.sent:
        sentRecords.value = records;
        break;
      case RecordType.received:
        receivedRecords.value = records;
        break;
      case RecordType.collected:
        collectedRecords.value = records;
        break;
      case RecordType.collecting:
        collectingRecords.value = records;
        break;
    }
    // 数据更新后，同步更新筛选缓存
    _updateFilteredRecords(type);
  }

  /// 添加记录（加载更多时使用）
  void _addRecords(RecordType type, List<RecordModel> records) {
    switch (type) {
      case RecordType.sent:
        sentRecords.addAll(records);
        break;
      case RecordType.received:
        receivedRecords.addAll(records);
        break;
      case RecordType.collected:
        collectedRecords.addAll(records);
        break;
      case RecordType.collecting:
        collectingRecords.addAll(records);
        break;
    }
    // 数据更新后，同步更新筛选缓存
    _updateFilteredRecords(type);
  }

  /// 刷新当前Tab数据
  Future<void> refreshCurrentTab() async {
    final type = RecordType.values[currentTabIndex.value];
    await loadRecords(type, isRefresh: true);
  }

  /// 加载更多数据
  Future<void> loadMore() async {
    if (!hasMore.value || isLoading.value) return;

    final type = RecordType.values[currentTabIndex.value];
    await loadRecords(type);
  }

  /// 搜索
  void search(String keyword) {
    searchKeyword.value = keyword;
  }

  /// 筛选状态
  void filterByStatus(RecordStatus? status) {
    selectedStatus.value = status;
  }

  /// 清除筛选
  void clearFilters() {
    searchKeyword.value = '';
    selectedStatus.value = null;
  }

  /// 删除记录
  Future<void> deleteRecord(String recordId) async {
    try {
      // TODO: 调用API删除记录
      await Future.delayed(const Duration(milliseconds: 500));

      // 从所有列表中移除
      sentRecords.removeWhere((record) => record.id == recordId);
      receivedRecords.removeWhere((record) => record.id == recordId);
      collectedRecords.removeWhere((record) => record.id == recordId);
      collectingRecords.removeWhere((record) => record.id == recordId);

      Get.snackbar('成功', '记录已删除');
    } catch (e) {
      Get.snackbar('错误', '删除失败: $e');
    }
  }

  /// 点赞记录
  Future<void> likeRecord(String recordId) async {
    try {
      // TODO: 调用API点赞
      await Future.delayed(const Duration(milliseconds: 300));

      // 更新本地数据
      _updateRecordSocial(recordId, (social) {
        return social.copyWith(likes: social.likes + 1);
      });

      Get.snackbar('成功', '已点赞', duration: Duration(milliseconds: 800));
    } catch (e) {
      Get.snackbar('错误', '点赞失败: $e');
    }
  }

  /// 感谢记录
  Future<void> thankRecord(String recordId) async {
    try {
      // TODO: 调用API感谢
      await Future.delayed(const Duration(milliseconds: 300));

      // 更新本地数据
      _updateRecordSocial(recordId, (social) {
        return social.copyWith(thanks: social.thanks + 1);
      });

      Get.snackbar('成功', '已感谢', duration: Duration(milliseconds: 800));
    } catch (e) {
      Get.snackbar('错误', '感谢失败: $e');
    }
  }

  /// 添加表情反应
  Future<void> addEmojiReaction(String recordId, String emoji) async {
    try {
      // TODO: 调用API添加表情反应
      await Future.delayed(const Duration(milliseconds: 300));

      // 更新本地数据
      _updateRecordSocial(recordId, (social) {
        final reactions = Map<String, int>.from(social.reactions);
        reactions[emoji] = (reactions[emoji] ?? 0) + 1;
        return social.copyWith(reactions: reactions);
      });

      Get.snackbar('成功', '已添加表情反应', duration: Duration(milliseconds: 800));
    } catch (e) {
      Get.snackbar('错误', '添加表情反应失败: $e');
    }
  }

  /// 添加标签
  Future<void> addTag(String recordId, String tag) async {
    try {
      // TODO: 调用API添加标签
      await Future.delayed(const Duration(milliseconds: 300));

      // 更新本地数据
      _updateRecordSocial(recordId, (social) {
        final tags = List<String>.from(social.tags);
        if (!tags.contains(tag)) {
          tags.add(tag);
        }
        return social.copyWith(tags: tags);
      });

      Get.snackbar('成功', '已添加标签', duration: Duration(milliseconds: 800));
    } catch (e) {
      Get.snackbar('错误', '添加标签失败: $e');
    }
  }

  /// 更新记录的社交数据
  void _updateRecordSocial(String recordId, SocialInteraction Function(SocialInteraction) updater) {
    // 更新所有列表中的记录
    _updateRecordInList(sentRecords, recordId, updater);
    _updateRecordInList(receivedRecords, recordId, updater);
    _updateRecordInList(collectedRecords, recordId, updater);
    _updateRecordInList(collectingRecords, recordId, updater);
  }

  /// 更新列表中的记录
  void _updateRecordInList(
      RxList<RecordModel> list, String recordId, SocialInteraction Function(SocialInteraction) updater) {
    final index = list.indexWhere((record) => record.id == recordId);
    if (index != -1) {
      final record = list[index];
      final updatedSocial = updater(record.social);
      list[index] = record.copyWith(social: updatedSocial);
    }
  }

  /// 复制分享链接
  void copyShareUrl(RecordModel record) {
    if (record.shareUrl != null) {
      // 这里应该调用剪贴板API
      Get.snackbar('成功', '分享链接已复制到剪贴板');
    }
  }

  /// 重新分享
  void reshare(RecordModel record) {
    // 跳转到发送页面，预填充数据
    Get.toNamed('/send', arguments: {
      'files': record.files,
      'title': record.title,
      'description': record.description,
    });
  }

  /// 查看详情
  void viewDetails(RecordModel record) {
    // 跳转到详情页面，使用路径参数传递recordId
    Get.toNamed('/record-detail/${record.id}');
  }

  /// 切换搜索框展开状态
  void toggleSearchExpanded() {
    isSearchExpanded.value = !isSearchExpanded.value;

    // 如果是展开搜索框，重置搜索状态和动画
    if (isSearchExpanded.value) {
      try {
        final searchController = Get.find<SearchFieldController>();
        searchController.resetForNewSearch();
      } catch (e) {
        // 如果搜索控制器未找到，忽略错误
        debugPrint('SearchFieldController not found: $e');
      }
    }
  }

  /// 根据类型获取缓存的筛选结果
  List<RecordModel> _getFilteredRecordsByType(RecordType type) {
    switch (type) {
      case RecordType.sent:
        return filteredSentRecords;
      case RecordType.received:
        return filteredReceivedRecords;
      case RecordType.collected:
        return filteredCollectedRecords;
      case RecordType.collecting:
        return filteredCollectingRecords;
    }
  }

  /// 更新所有Tab的筛选缓存
  void _updateAllFilteredRecords() {
    _updateFilteredRecords(RecordType.sent);
    _updateFilteredRecords(RecordType.received);
    _updateFilteredRecords(RecordType.collected);
    _updateFilteredRecords(RecordType.collecting);
  }

  /// 更新指定类型的筛选缓存
  void _updateFilteredRecords(RecordType type) {
    final originalRecords = getRecordsByType(type);
    final filteredRecords = getFilteredRecords(originalRecords);

    switch (type) {
      case RecordType.sent:
        filteredSentRecords.assignAll(filteredRecords);
        break;
      case RecordType.received:
        filteredReceivedRecords.assignAll(filteredRecords);
        break;
      case RecordType.collected:
        filteredCollectedRecords.assignAll(filteredRecords);
        break;
      case RecordType.collecting:
        filteredCollectingRecords.assignAll(filteredRecords);
        break;
    }
  }
}
