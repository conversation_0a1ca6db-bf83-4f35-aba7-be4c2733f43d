import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/record_model.dart';
import '../controllers/record_detail_controller.dart';
import '../../../../core/theme/theme_helper.dart';

import '../../../global_widgets/buttons/quick_action_button.dart';

class RecordDetailPage extends GetView<RecordDetailController> {
  const RecordDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Material(
      color: Colors.transparent,
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          _buildSliverAppBar(context, theme),
        ],
        body: _buildFileList(context, theme),
      ),
    );
  }

  /// 构建顶部AppBar - 产品化重新设计
  Widget _buildSliverAppBar(BuildContext context, AppTheme theme) {
    return SliverAppBar(
      elevation: 0,
      centerTitle: false,
      floating: false,
      snap: false,
      pinned: true,
      expandedHeight: 500, // 增加高度以容纳更丰富的内容
      automaticallyImplyLeading: false,
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: _buildModernHeaderContent(context, theme),
      ),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(120), // 增加底部高度
        child: _buildSmartActionBar(context, theme),
      ),
    );
  }

  /// 构建现代化头部内容 - 产品化设计
  Widget _buildModernHeaderContent(BuildContext context, AppTheme theme) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF667EEA), // 现代蓝紫渐变
            const Color(0xFF764BA2),
          ],
          stops: const [0.0, 1.0],
        ),
      ),
      child: SafeArea(
        child: Stack(
          children: [
            // 动态背景装饰
            _buildDynamicBackground(),
            // 导航栏
            _buildNavigationBar(context, theme),
            // 主要内容区域
            Positioned(
              top: 60,
              left: 0,
              right: 0,
              bottom: 0,
              child: _buildHeaderMainContent(context, theme),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建动态背景装饰
  Widget _buildDynamicBackground() {
    return Stack(
      children: [
        // 主装饰圆
        Positioned(
          top: -80,
          right: -60,
          child: Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  Colors.white.withValues(alpha: 0.15),
                  Colors.white.withValues(alpha: 0.05),
                ],
              ),
            ),
          ),
        ),
        // 次要装饰圆
        Positioned(
          bottom: -40,
          left: -40,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  Colors.white.withValues(alpha: 0.1),
                  Colors.white.withValues(alpha: 0.03),
                ],
              ),
            ),
          ),
        ),
        // 小装饰点
        Positioned(
          top: 120,
          left: 40,
          child: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withValues(alpha: 0.3),
            ),
          ),
        ),
        Positioned(
          top: 180,
          right: 80,
          child: Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withValues(alpha: 0.4),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建导航栏
  Widget _buildNavigationBar(BuildContext context, AppTheme theme) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 60,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            // 返回按钮
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: IconButton(
                onPressed: () => Get.back(),
                icon: const Icon(
                  LucideIcons.arrowLeft,
                  color: Colors.white,
                  size: 20,
                ),
                padding: EdgeInsets.zero,
              ),
            ),
            const Spacer(),
            // 操作按钮组
            Row(
              children: [
                _buildNavActionButton(LucideIcons.share2, () {}),
                const SizedBox(width: 8),
                _buildNavActionButton(LucideIcons.ellipsisVertical, () {}),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建导航操作按钮
  Widget _buildNavActionButton(IconData icon, VoidCallback onTap) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: IconButton(
        onPressed: onTap,
        icon: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }

  /// 构建头部主要内容
  Widget _buildHeaderMainContent(BuildContext context, AppTheme theme) {
    return Obx(() {
      final record = controller.record.value;
      if (record == null) return const SizedBox();

      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 传输状态指示器
            _buildTransferStatusIndicator(record),
            const SizedBox(height: 16),
            // 主要信息卡片
            _buildMainInfoCard(record, theme),
            const SizedBox(height: 20),
            // 快速统计信息
            _buildQuickStats(record),
          ],
        ),
      );
    });
  }

  /// 构建传输状态指示器
  Widget _buildTransferStatusIndicator(RecordModel record) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.greenAccent,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '传输完成',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建主要信息卡片
  Widget _buildMainInfoCard(RecordModel record, AppTheme theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.25),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 接收者信息
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  LucideIcons.users,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '发送给',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${record.title.isNotEmpty ? record.title : '易家千纸鹤'} 等${record.recipients.length}人',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 时间信息
          Row(
            children: [
              Icon(
                LucideIcons.clock,
                color: Colors.white.withValues(alpha: 0.8),
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                _formatDateTime(record.createdAt),
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          // 描述信息（如果有）
          if (record.description != null && record.description!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                record.description!,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: 14,
                  height: 1.4,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建快速统计信息
  Widget _buildQuickStats(RecordModel record) {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            LucideIcons.files,
            '${record.files.length}',
            '个文件',
            Colors.blue[300]!,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatItem(
            LucideIcons.hardDrive,
            _formatFileSize(_getTotalFileSize(record.files)),
            '总大小',
            Colors.green[300]!,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatItem(
            LucideIcons.timer,
            _getExpiryDays(record.expiresAt ?? DateTime.now()),
            '天后到期',
            Colors.orange[300]!,
          ),
        ),
      ],
    );
  }

  /// 构建统计项
  Widget _buildStatItem(IconData icon, String value, String label, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w700,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建智能操作栏 - 产品化设计
  Widget _buildSmartActionBar(BuildContext context, AppTheme theme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          // 快速操作按钮组
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    icon: LucideIcons.download,
                    label: '全部下载',
                    color: theme.primary,
                    onTap: () {},
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    icon: LucideIcons.share2,
                    label: '分享链接',
                    color: Colors.green,
                    onTap: () {},
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    icon: LucideIcons.copy,
                    label: '复制链接',
                    color: Colors.orange,
                    onTap: () {},
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // 文件列表标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            child: Row(
              children: [
                Icon(
                  LucideIcons.folder,
                  size: 20,
                  color: theme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  '文件列表',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.onSurface,
                  ),
                ),
                const Spacer(),
                Obx(() => Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: theme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${controller.record.value?.files.length ?? 0} 个文件',
                        style: TextStyle(
                          color: theme.primary,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    )),
                const SizedBox(width: 12),
                Obx(() => QuickActionButton(
                      theme: theme,
                      onTap: () {
                        controller.toggleViewMode();
                      },
                      icon: controller.viewMode.value == 'list' ? LucideIcons.grid3x3 : LucideIcons.list,
                    )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取到期天数
  String _getExpiryDays(DateTime expiresAt) {
    final now = DateTime.now();
    final difference = expiresAt.difference(now).inDays;
    return difference > 0 ? difference.toString() : '已过期';
  }

  /// 构建文件列表头部 - 简化版本
  Widget _buildFileListHeader(BuildContext context, AppTheme theme) {
    return const SizedBox.shrink(); // 头部功能已移至智能操作栏
  }

  /// 构建文件列表
  Widget _buildFileList(BuildContext context, AppTheme theme) {
    return Material(
      color: theme.surface,
      child: Column(
        children: [
          _buildFileListHeader(context, theme),
          Expanded(
            child: Obx(() {
              final record = controller.record.value;

              if (record == null) {
                return Center(
                  child: CircularProgressIndicator(
                    color: theme.primary,
                  ),
                );
              }

              if (record.files.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        LucideIcons.fileX,
                        size: 64,
                        color: theme.onSurface.withValues(alpha: 0.3),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        '暂无文件',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                );
              }

              return controller.viewMode.value == 'grid'
                  ? _buildFileGridView(context, theme, record.files)
                  : _buildFileListView(context, theme, record.files);
            }),
          ),
        ],
      ),
    );
  }

  /// 构建文件网格视图
  Widget _buildFileGridView(BuildContext context, AppTheme theme, List<FileInfo> files) {
    return OrientationBuilder(
      builder: (context, orientation) {
        return GridView.builder(
          padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: orientation == Orientation.landscape ? 3 : 2,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1.2,
          ),
          itemCount: files.length,
          itemBuilder: (context, index) {
            return _buildFileGridItem(context, theme, files[index]);
          },
        );
      },
    );
  }

  /// 构建文件列表视图
  Widget _buildFileListView(BuildContext context, AppTheme theme, List<FileInfo> files) {
    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
      itemCount: files.length,
      itemBuilder: (context, index) {
        return _buildFileItem(context, theme, files[index]);
      },
    );
  }

  /// 构建文件网格项
  Widget _buildFileGridItem(BuildContext context, AppTheme theme, FileInfo file) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _showFileOptions(file),
        borderRadius: BorderRadius.circular(12),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            color: theme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.outline.withValues(alpha: 0.08),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: theme.shadow.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // 主要内容区域
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    children: [
                      // 顶部：文件图标和操作按钮
                      Row(
                        children: [
                          // 文件图标
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: _getFileTypeColor(file.type).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: _getFileTypeColor(file.type).withValues(alpha: 0.2),
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Icon(
                              _getFileIcon(file.type),
                              color: _getFileTypeColor(file.type),
                              size: 20,
                            ),
                          ),
                          const Spacer(),
                          // 更多操作按钮
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: () => _showFileOptions(file),
                              borderRadius: BorderRadius.circular(6),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  LucideIcons.ellipsisVertical,
                                  size: 14,
                                  color: theme.onSurface.withValues(alpha: 0.4),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                      // 文件名
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          file.name,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 4),
                      // 文件类型和大小
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                            decoration: BoxDecoration(
                              color: _getFileTypeColor(file.type).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(3),
                            ),
                            child: Text(
                              file.type.toUpperCase(),
                              style: theme.textTheme.labelSmall?.copyWith(
                                fontSize: 9,
                                fontWeight: FontWeight.w500,
                                color: _getFileTypeColor(file.type),
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              _formatFileSize(file.size),
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontSize: 10,
                                color: theme.onSurface.withValues(alpha: 0.6),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              // 底部快捷操作栏
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                  color: theme.onSurface.withValues(alpha: 0.03),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    QuickActionButton(
                      icon: LucideIcons.share2,
                      onTap: () => _shareFile(file),
                      theme: theme,
                    ),
                    Container(
                      width: 1,
                      height: 12,
                      color: theme.outline.withValues(alpha: 0.1),
                    ),
                    QuickActionButton(
                      icon: LucideIcons.download,
                      onTap: () => _downloadFile(file),
                      theme: theme,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建文件列表项
  Widget _buildFileItem(BuildContext context, AppTheme theme, FileInfo file) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showFileOptions(file),
          borderRadius: BorderRadius.circular(12),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: theme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.outline.withValues(alpha: 0.08),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.shadow.withValues(alpha: 0.04),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // 文件图标
                  Hero(
                    tag: 'file_icon_${file.name}',
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            _getFileTypeColor(file.type).withValues(alpha: 0.15),
                            _getFileTypeColor(file.type).withValues(alpha: 0.05),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: _getFileTypeColor(file.type).withValues(alpha: 0.2),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        _getFileIcon(file.type),
                        color: _getFileTypeColor(file.type),
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // 文件信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          file.name,
                          style: theme.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 6),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: _getFileTypeColor(file.type).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                file.type.toUpperCase(),
                                style: theme.textTheme.labelSmall?.copyWith(
                                  color: _getFileTypeColor(file.type),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              LucideIcons.hardDrive,
                              size: 12,
                              color: theme.onSurface.withValues(alpha: 0.4),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _formatFileSize(file.size),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // 快捷操作按钮
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      QuickActionButton(
                        icon: LucideIcons.share2,
                        onTap: () => _shareFile(file),
                        theme: theme,
                      ),
                      const SizedBox(width: 8),
                      QuickActionButton(
                        icon: LucideIcons.download,
                        onTap: () => _downloadFile(file),
                        theme: theme,
                      ),
                      const SizedBox(width: 8),
                      QuickActionButton(
                        onTap: () => _showFileOptions(file),
                        icon: LucideIcons.ellipsisVertical,
                        theme: theme,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 获取文件图标
  IconData _getFileIcon(String fileType) {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return LucideIcons.fileText;
      case 'doc':
      case 'docx':
        return LucideIcons.fileText;
      case 'xls':
      case 'xlsx':
        return LucideIcons.fileSpreadsheet;
      case 'ppt':
      case 'pptx':
        return LucideIcons.presentation;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return LucideIcons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return LucideIcons.video;
      case 'mp3':
      case 'wav':
      case 'flac':
        return LucideIcons.music;
      case 'zip':
      case 'rar':
      case '7z':
        return LucideIcons.archive;
      default:
        return LucideIcons.file;
    }
  }

  /// 获取文件类型颜色
  Color _getFileTypeColor(String fileType) {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return const Color(0xFFE53E3E);
      case 'doc':
      case 'docx':
        return const Color(0xFF2B6CB0);
      case 'xls':
      case 'xlsx':
        return const Color(0xFF38A169);
      case 'ppt':
      case 'pptx':
        return const Color(0xFFD69E2E);
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return const Color(0xFF9F7AEA);
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return const Color(0xFFED8936);
      case 'mp3':
      case 'wav':
      case 'flac':
        return const Color(0xFF48BB78);
      case 'zip':
      case 'rar':
      case '7z':
        return const Color(0xFF4299E1);
      default:
        return Colors.grey;
    }
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// 格式化到期时间
  String _formatExpiryTime(DateTime? expiresAt) {
    if (expiresAt == null) return '永久有效';

    final now = DateTime.now();
    final difference = expiresAt.difference(now);

    if (difference.isNegative) {
      return '已过期';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}天后';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时后';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟后';
    } else {
      return '即将过期';
    }
  }

  /// 显示文件选项
  void _showFileOptions(FileInfo file) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),
            ListTile(
              leading: Icon(LucideIcons.eye),
              title: const Text('预览'),
              onTap: () {
                Get.back();
                // TODO: 实现文件预览功能
              },
            ),
            ListTile(
              leading: Icon(LucideIcons.download),
              title: const Text('下载'),
              onTap: () {
                Get.back();
                // TODO: 实现文件下载功能
              },
            ),
            ListTile(
              leading: Icon(LucideIcons.share2),
              title: const Text('分享'),
              onTap: () {
                Get.back();
                // TODO: 实现文件分享功能
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 分享文件
  void _shareFile(FileInfo file) {
    // TODO: 实现文件分享功能
    Get.snackbar('分享', '分享文件: ${file.name}');
  }

  /// 下载文件
  void _downloadFile(FileInfo file) {
    // TODO: 实现文件下载功能
    Get.snackbar('下载', '下载文件: ${file.name}');
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return DateFormat('HH:mm').format(dateTime);
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
    }
  }

  /// 计算文件总大小
  int _getTotalFileSize(List<FileInfo> files) {
    return files.fold(0, (total, file) => total + file.size);
  }
}
