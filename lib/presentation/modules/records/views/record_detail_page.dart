import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/record_model.dart';
import '../controllers/record_detail_controller.dart';
import '../../../../core/theme/theme_helper.dart';

import '../../../global_widgets/buttons/quick_action_button.dart';

class RecordDetailPage extends GetView<RecordDetailController> {
  const RecordDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Material(
      color: Colors.transparent,
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          _buildSliverAppBar(context, theme),
        ],
        body: _buildFileList(context, theme),
      ),
    );
  }

  /// 构建顶部AppBar - 根据设计图重新设计
  Widget _buildSliverAppBar(BuildContext context, AppTheme theme) {
    return SliverAppBar(
      elevation: 0,
      centerTitle: false,
      floating: false,
      snap: false,
      pinned: true,
      expandedHeight: 300, // 紧凑高度
      automaticallyImplyLeading: false,
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      leading: _buildSenderAvatar(context, theme),
      title: _buildNavigationTitle(context, theme),
      actions: [
        IconButton(
          onPressed: () {},
          icon: const Icon(
            LucideIcons.share2,
            color: Colors.black87,
            size: 24,
          ),
        ),
        IconButton(
          onPressed: () {},
          icon: const Icon(
            LucideIcons.ellipsisVertical,
            color: Colors.black87,
            size: 24,
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: _buildDescriptionContent(context, theme),
      ),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60), // 紧凑高度
        child: _buildStatsSection(context, theme),
      ),
    );
  }

  /// 构建发送者头像 - 点击返回或进入主页
  Widget _buildSenderAvatar(BuildContext context, AppTheme theme) {
    return GestureDetector(
      onTap: () {
        // 尝试返回，如果无法返回则进入主页
        try {
          Get.back();
        } catch (e) {
          Get.offAllNamed('/home'); // 进入主页，清除所有路由栈
        }
      },
      child: Container(
        margin: const EdgeInsets.only(left: 16),
        child: CircleAvatar(
          radius: 18,
          backgroundColor: theme.primary.withValues(alpha: 0.1),
          child: Icon(
            LucideIcons.user,
            color: theme.primary,
            size: 20,
          ),
        ),
      ),
    );
  }

  /// 构建导航标题 - 包含接收者和描述信息
  Widget _buildNavigationTitle(BuildContext context, AppTheme theme) {
    return Obx(() {
      final record = controller.record.value;
      if (record == null) return const SizedBox();

      return GestureDetector(
        onTap: () {
          // 点击展开描述内容
          if (record.description != null && record.description!.isNotEmpty) {
            _showDescriptionDialog(record.description!);
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 接收者信息
            Text(
              '${record.title.isNotEmpty ? record.title : '易家千纸鹤'} 等${record.recipients.length}人',
              style: const TextStyle(
                color: Colors.black87,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            // 描述信息（可点击展开）
            if (record.description != null && record.description!.isNotEmpty)
              Row(
                children: [
                  Expanded(
                    child: Text(
                      record.description!,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    LucideIcons.chevronDown,
                    size: 14,
                    color: Colors.grey[600],
                  ),
                ],
              ),
          ],
        ),
      );
    });
  }

  /// 构建发送文件详情统计信息 - 水平布局小卡片
  Widget _buildDescriptionContent(BuildContext context, AppTheme theme) {
    return Obx(() {
      final record = controller.record.value;
      if (record == null) return const SizedBox();

      return Container(
        color: Colors.white,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 12), // 紧凑间距
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildStatCard(
                    icon: LucideIcons.clock,
                    label: '到期时间',
                    value: _getExpiryDays(record.expiresAt ?? DateTime.now().add(const Duration(days: 3))),
                    unit: '天后',
                    theme: theme,
                  ),
                  const SizedBox(width: 12),
                  _buildStatCard(
                    icon: LucideIcons.files,
                    label: '文件数量',
                    value: '${record.files.length}',
                    unit: '个',
                    theme: theme,
                  ),
                  const SizedBox(width: 12),
                  _buildStatCard(
                    icon: LucideIcons.hardDrive,
                    label: '文件大小',
                    value: _formatFileSize(_getTotalFileSize(record.files)),
                    unit: '',
                    theme: theme,
                  ),
                  const SizedBox(width: 12),
                  _buildStatCard(
                    icon: LucideIcons.download,
                    label: '下载次数',
                    value: '12', // 模拟数据
                    unit: '次',
                    theme: theme,
                  ),
                  const SizedBox(width: 12),
                  _buildStatCard(
                    icon: LucideIcons.eye,
                    label: '预览次数',
                    value: '28', // 模拟数据
                    unit: '次',
                    theme: theme,
                  ),
                  const SizedBox(width: 12),
                  _buildStatCard(
                    icon: LucideIcons.share2,
                    label: '转发次数',
                    value: '5', // 模拟数据
                    unit: '次',
                    theme: theme,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  /// 构建文件列表标题和操作按钮区域
  Widget _buildStatsSection(BuildContext context, AppTheme theme) {
    return Obx(() {
      final record = controller.record.value;
      if (record == null) return const SizedBox();

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Colors.grey[200]!,
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            // 文件列表标题
            Expanded(
              child: Text(
                '文件列表 (${record.files.length})',
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            // 搜索按钮
            IconButton(
              onPressed: () {
                // TODO: 实现搜索功能
                Get.snackbar('搜索', '搜索功能开发中');
              },
              icon: Icon(
                LucideIcons.search,
                color: Colors.grey[600],
                size: 20,
              ),
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(
                minWidth: 36,
                minHeight: 36,
              ),
            ),
            // 视图切换按钮
            IconButton(
              onPressed: () {
                // TODO: 实现视图切换功能
                Get.snackbar('视图切换', '视图切换功能开发中');
              },
              icon: Icon(
                LucideIcons.grid3x3,
                color: Colors.grey[600],
                size: 20,
              ),
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(
                minWidth: 36,
                minHeight: 36,
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 构建统计卡片 - 简洁小卡片设计
  Widget _buildStatCard({
    required IconData icon,
    required String label,
    required String value,
    required String unit,
    required AppTheme theme,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: theme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              color: theme.primary,
              size: 16,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                value,
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (unit.isNotEmpty)
                Text(
                  unit,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// 显示描述内容对话框
  void _showDescriptionDialog(String description) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Expanded(
                    child: Text(
                      '描述内容',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(LucideIcons.x),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                description,
                style: const TextStyle(
                  fontSize: 14,
                  height: 1.5,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 20),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('关闭'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取到期天数
  String _getExpiryDays(DateTime expiresAt) {
    final now = DateTime.now();
    final difference = expiresAt.difference(now).inDays;
    return difference > 0 ? difference.toString() : '已过期';
  }

  /// 构建文件列表 - 包含标题栏
  Widget _buildFileList(BuildContext context, AppTheme theme) {
    return Material(
      color: theme.surface,
      child: Column(
        children: [
          // 文件列表内容
          Expanded(
            child: Obx(() {
              final record = controller.record.value;

              if (record == null) {
                return Center(
                  child: CircularProgressIndicator(
                    color: theme.primary,
                  ),
                );
              }

              if (record.files.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        LucideIcons.fileX,
                        size: 64,
                        color: theme.onSurface.withValues(alpha: 0.3),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        '暂无文件',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                );
              }

              return controller.viewMode.value == 'grid'
                  ? _buildFileGridView(context, theme, record.files)
                  : _buildFileListView(context, theme, record.files);
            }),
          ),
        ],
      ),
    );
  }

  /// 构建文件网格视图
  Widget _buildFileGridView(BuildContext context, AppTheme theme, List<FileInfo> files) {
    return OrientationBuilder(
      builder: (context, orientation) {
        return GridView.builder(
          padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: orientation == Orientation.landscape ? 3 : 2,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1.2,
          ),
          itemCount: files.length,
          itemBuilder: (context, index) {
            return _buildFileGridItem(context, theme, files[index]);
          },
        );
      },
    );
  }

  /// 构建文件列表视图
  Widget _buildFileListView(BuildContext context, AppTheme theme, List<FileInfo> files) {
    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
      itemCount: files.length,
      itemBuilder: (context, index) {
        return _buildFileItem(context, theme, files[index]);
      },
    );
  }

  /// 构建文件网格项
  Widget _buildFileGridItem(BuildContext context, AppTheme theme, FileInfo file) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _showFileOptions(file),
        borderRadius: BorderRadius.circular(12),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            color: theme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.outline.withValues(alpha: 0.08),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: theme.shadow.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // 主要内容区域
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    children: [
                      // 顶部：文件图标和操作按钮
                      Row(
                        children: [
                          // 文件图标
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: _getFileTypeColor(file.type).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: _getFileTypeColor(file.type).withValues(alpha: 0.2),
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Icon(
                              _getFileIcon(file.type),
                              color: _getFileTypeColor(file.type),
                              size: 20,
                            ),
                          ),
                          const Spacer(),
                          // 更多操作按钮
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: () => _showFileOptions(file),
                              borderRadius: BorderRadius.circular(6),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: Icon(
                                  LucideIcons.ellipsisVertical,
                                  size: 14,
                                  color: theme.onSurface.withValues(alpha: 0.4),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                      // 文件名
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          file.name,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 4),
                      // 文件类型和大小
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                            decoration: BoxDecoration(
                              color: _getFileTypeColor(file.type).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(3),
                            ),
                            child: Text(
                              file.type.toUpperCase(),
                              style: theme.textTheme.labelSmall?.copyWith(
                                fontSize: 9,
                                fontWeight: FontWeight.w500,
                                color: _getFileTypeColor(file.type),
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              _formatFileSize(file.size),
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontSize: 10,
                                color: theme.onSurface.withValues(alpha: 0.6),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              // 底部快捷操作栏
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                  color: theme.onSurface.withValues(alpha: 0.03),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    QuickActionButton(
                      icon: LucideIcons.share2,
                      onTap: () => _shareFile(file),
                      theme: theme,
                    ),
                    Container(
                      width: 1,
                      height: 12,
                      color: theme.outline.withValues(alpha: 0.1),
                    ),
                    QuickActionButton(
                      icon: LucideIcons.download,
                      onTap: () => _downloadFile(file),
                      theme: theme,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建文件列表项
  Widget _buildFileItem(BuildContext context, AppTheme theme, FileInfo file) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showFileOptions(file),
          borderRadius: BorderRadius.circular(12),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: theme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.outline.withValues(alpha: 0.08),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.shadow.withValues(alpha: 0.04),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // 文件图标
                  Hero(
                    tag: 'file_icon_${file.name}',
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            _getFileTypeColor(file.type).withValues(alpha: 0.15),
                            _getFileTypeColor(file.type).withValues(alpha: 0.05),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: _getFileTypeColor(file.type).withValues(alpha: 0.2),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        _getFileIcon(file.type),
                        color: _getFileTypeColor(file.type),
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // 文件信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          file.name,
                          style: theme.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 6),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: _getFileTypeColor(file.type).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                file.type.toUpperCase(),
                                style: theme.textTheme.labelSmall?.copyWith(
                                  color: _getFileTypeColor(file.type),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              LucideIcons.hardDrive,
                              size: 12,
                              color: theme.onSurface.withValues(alpha: 0.4),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _formatFileSize(file.size),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // 快捷操作按钮
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      QuickActionButton(
                        icon: LucideIcons.share2,
                        onTap: () => _shareFile(file),
                        theme: theme,
                      ),
                      const SizedBox(width: 8),
                      QuickActionButton(
                        icon: LucideIcons.download,
                        onTap: () => _downloadFile(file),
                        theme: theme,
                      ),
                      const SizedBox(width: 8),
                      QuickActionButton(
                        onTap: () => _showFileOptions(file),
                        icon: LucideIcons.ellipsisVertical,
                        theme: theme,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 获取文件图标
  IconData _getFileIcon(String fileType) {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return LucideIcons.fileText;
      case 'doc':
      case 'docx':
        return LucideIcons.fileText;
      case 'xls':
      case 'xlsx':
        return LucideIcons.fileSpreadsheet;
      case 'ppt':
      case 'pptx':
        return LucideIcons.presentation;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return LucideIcons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return LucideIcons.video;
      case 'mp3':
      case 'wav':
      case 'flac':
        return LucideIcons.music;
      case 'zip':
      case 'rar':
      case '7z':
        return LucideIcons.archive;
      default:
        return LucideIcons.file;
    }
  }

  /// 获取文件类型颜色
  Color _getFileTypeColor(String fileType) {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return const Color(0xFFE53E3E);
      case 'doc':
      case 'docx':
        return const Color(0xFF2B6CB0);
      case 'xls':
      case 'xlsx':
        return const Color(0xFF38A169);
      case 'ppt':
      case 'pptx':
        return const Color(0xFFD69E2E);
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return const Color(0xFF9F7AEA);
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return const Color(0xFFED8936);
      case 'mp3':
      case 'wav':
      case 'flac':
        return const Color(0xFF48BB78);
      case 'zip':
      case 'rar':
      case '7z':
        return const Color(0xFF4299E1);
      default:
        return Colors.grey;
    }
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// 显示文件选项
  void _showFileOptions(FileInfo file) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),
            ListTile(
              leading: Icon(LucideIcons.eye),
              title: const Text('预览'),
              onTap: () {
                Get.back();
                // TODO: 实现文件预览功能
              },
            ),
            ListTile(
              leading: Icon(LucideIcons.download),
              title: const Text('下载'),
              onTap: () {
                Get.back();
                // TODO: 实现文件下载功能
              },
            ),
            ListTile(
              leading: Icon(LucideIcons.share2),
              title: const Text('分享'),
              onTap: () {
                Get.back();
                // TODO: 实现文件分享功能
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 分享文件
  void _shareFile(FileInfo file) {
    // TODO: 实现文件分享功能
    Get.snackbar('分享', '分享文件: ${file.name}');
  }

  /// 下载文件
  void _downloadFile(FileInfo file) {
    // TODO: 实现文件下载功能
    Get.snackbar('下载', '下载文件: ${file.name}');
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return DateFormat('HH:mm').format(dateTime);
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
    }
  }

  /// 计算文件总大小
  int _getTotalFileSize(List<FileInfo> files) {
    return files.fold(0, (total, file) => total + file.size);
  }
}
