import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:glass/glass.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../controllers/records_controller.dart';
import '../models/record_model.dart';
import '../widgets/record_card.dart';
import '../widgets/records_tab_bar.dart';
import '../widgets/records_filter_chips.dart';
import '../widgets/state_widget/loading_state_widget.dart';
import '../widgets/state_widget/empty_state_widget.dart';
import '../widgets/state_widget/load_more_indicator_widget.dart';
import '../../../global_widgets/buttons/unified_header_action_button.dart';
import '../../../global_widgets/search/modern_search_field_widget.dart';
import '../../../../core/theme/theme_helper.dart';

class RecordsPage extends GetWidget<RecordsController> {
  const RecordsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Material(
      color: Colors.transparent,
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          // 现代化应用栏
          Obx(() => SliverAppBar(
                expandedHeight: controller.isSearchExpanded.value ? 180 : 120,
                floating: true,
                pinned: true,
                elevation: 0,
                centerTitle: false,
                automaticallyImplyLeading: false,
                backgroundColor: theme.surface.withValues(alpha: 0.1),
                surfaceTintColor: Colors.transparent,
                flexibleSpace: FlexibleSpaceBar(
                  background: SafeArea(
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(8, 0, 8, 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 标题区域（优化版）
                          Row(
                            children: [
                              // 标题文本
                              Expanded(
                                child: Text(
                                  '记录',
                                  style: theme.getTextStyle(
                                    fontSize: AppTheme.fontSize24,
                                    fontWeight: AppTheme.fontWeightBold,
                                    color: theme.onSurface,
                                  ),
                                ),
                              ),
                              // 操作按钮组
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // 搜索按钮
                                  UnifiedHeaderActionButton.modern(
                                    icon: controller.isSearchExpanded.value ? LucideIcons.searchX : LucideIcons.search,
                                    onPressed: controller.toggleSearchExpanded,
                                    isActive: controller.isSearchExpanded.value,
                                  ),
                                  SizedBox(width: AppTheme.spacingHorizontal8),
                                  // 添加按钮
                                  UnifiedHeaderActionButton.modern(
                                    icon: LucideIcons.packagePlus,
                                    onPressed: () => Get.toNamed('/send'),
                                    isPrimary: true,
                                  ),
                                ],
                              ),
                            ],
                          ),
                          // 搜索框 - 点击展开显示
                          if (controller.isSearchExpanded.value) ...[
                            SizedBox(height: 8),
                            ModernSearchFieldWidget(
                              onChanged: controller.search,
                              hintText: '搜索记录、文件名或描述...',
                              autofocus: true,
                              enableVoiceSearch: true,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ).asGlass(
                  enabled: true,
                  tintColor: Colors.transparent,
                  blurX: 5,
                  blurY: 5,
                  frosted: false,
                ),
                // 底部TabBar
                bottom: PreferredSize(
                  preferredSize: Size.fromHeight(72), // 只为TabBar预留高度
                  child: RecordsTabBar(),
                ),
              )),
        ],
        body: Column(
          children: [
            // 筛选栏区域
            Container(
              padding: EdgeInsets.symmetric(vertical: 2),
              decoration: BoxDecoration(
                color: theme.surface,
                border: Border(
                  bottom: BorderSide(
                    color: theme.shadow.withValues(alpha: 0.3),
                    width: 0.5,
                  ),
                ),
              ),
              child: RecordsFilterChips(),
            ),
            // TabBarView内容区域
            Expanded(
              child: _buildMaterialTabBarView(theme),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建Material TabBarView
  Widget _buildMaterialTabBarView(AppTheme theme) {
    return TabBarView(
      controller: controller.tabController,
      children: List.generate(4, (index) => _buildTabContent(theme, index)),
    );
  }

  /// 构建Tab内容
  Widget _buildTabContent(AppTheme theme, int tabIndex) {
    return Obx(() {
      // 根据tabIndex获取对应Tab的数据，避免TabBarView预加载导致的数据混乱
      final records = controller.getRecordsByTabIndex(tabIndex);
      final isLoading = controller.isLoading.value;
      final isRefreshing = controller.isRefreshing.value;
      final isCurrentTab = controller.currentTabIndex.value == tabIndex;

      // 显示加载状态的条件：数据为空且（非当前Tab 或 当前Tab正在加载且非刷新状态）
      if (records.isEmpty && (!isCurrentTab || (isLoading && !isRefreshing))) {
        return const LoadingStateWidget();
      }

      // 只有当前Tab且确实没有数据时才显示空状态
      if (isCurrentTab && records.isEmpty && !isLoading && !isRefreshing) {
        return EmptyStateWidget(
            type: tabIndex == 0
                ? RecordType.sent
                : tabIndex == 1
                    ? RecordType.received
                    : tabIndex == 2
                        ? RecordType.collected
                        : RecordType.collecting);
      }

      return RefreshIndicator(
        onRefresh: () => controller.refreshCurrentTab(),
        child: CustomScrollView(
          slivers: [
            SliverPadding(
              padding: AppTheme.paddingAll8,
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    if (index >= records.length) {
                      return const LoadMoreIndicatorWidget();
                    }

                    final record = records[index];
                    return RecordCard(
                      record: record,
                      onTap: () => controller.viewDetails(record),
                      onDelete: () => controller.deleteRecord(record.id),
                      onCopyLink: () => controller.copyShareUrl(record),
                      onReshare: () => controller.reshare(record),
                    );
                  },
                  childCount: records.length + (controller.hasMore.value ? 1 : 0),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
}
