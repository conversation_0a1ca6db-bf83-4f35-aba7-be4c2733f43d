import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 记录详情页面使用示例
/// 展示如何从其他页面导航到记录详情页面
class RecordDetailExample {
  /// 导航到记录详情页面
  /// 
  /// [recordId] 记录ID
  static void navigateToRecordDetail(String recordId) {
    Get.toNamed('/record-detail/$recordId');
  }
  
  /// 在记录列表中点击某个记录时的处理示例
  static Widget buildRecordListItem({
    required String recordId,
    required String title,
    required String description,
    required VoidCallback? onTap,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: Text(description),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: () {
        // 导航到记录详情页面
        navigateToRecordDetail(recordId);
        // 如果有额外的回调，也执行它
        onTap?.call();
      },
    );
  }
}

/// 使用示例：
/// 
/// 在记录列表页面中：
/// ```dart
/// RecordDetailExample.buildRecordListItem(
///   recordId: 'record_123',
///   title: '易家千纸鹤',
///   description: '客老的动画电影都在这里...',
///   onTap: () {
///     // 可选的额外处理
///     print('点击了记录');
///   },
/// )
/// ```
/// 
/// 或者直接导航：
/// ```dart
/// RecordDetailExample.navigateToRecordDetail('record_123');
/// ```