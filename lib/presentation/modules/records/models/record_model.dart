/// 记录类型枚举
enum RecordType {
  /// 我发送的
  sent,

  /// 发给我的
  received,

  /// 我收集的
  collected,

  /// 向我收集的
  collecting,
}

/// 记录状态枚举
enum RecordStatus {
  /// 进行中
  active,

  /// 已完成
  completed,

  /// 已过期
  expired,

  /// 已取消
  cancelled,
}

/// 记录操作枚举
enum RecordAction {
  /// 查看详情
  view,

  /// 下载
  download,

  /// 重新发送
  resend,

  /// 取消
  cancel,

  /// 删除
  delete,

  /// 分享
  share,

  /// 延期
  extend,

  /// 上传
  upload,

  // 重试
  retry,

  // 日程
  schedule,
}

/// 文件信息模型
class FileInfo {
  final String id;
  final String name;
  final String? thumbnailUrl;
  final int size;
  final String type;
  final DateTime uploadTime;
  final int downloadCount;

  FileInfo({
    required this.id,
    required this.name,
    this.thumbnailUrl,
    required this.size,
    required this.type,
    required this.uploadTime,
    this.downloadCount = 0,
  });

  factory FileInfo.fromJson(Map<String, dynamic> json) {
    return FileInfo(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      thumbnailUrl: json['thumbnailUrl'],
      size: json['size'] ?? 0,
      type: json['type'] ?? '',
      uploadTime: DateTime.parse(json['uploadTime'] ?? DateTime.now().toIso8601String()),
      downloadCount: json['downloadCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'thumbnailUrl': thumbnailUrl,
      'size': size,
      'type': type,
      'uploadTime': uploadTime.toIso8601String(),
      'downloadCount': downloadCount,
    };
  }

  /// 格式化文件大小
  String get formattedSize {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    if (size < 1024 * 1024 * 1024) return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

/// 用户信息模型
class UserInfo {
  final String id;
  final String name;
  final String? avatar;
  final String? email;

  UserInfo({
    required this.id,
    required this.name,
    this.avatar,
    this.email,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      avatar: json['avatar'],
      email: json['email'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'avatar': avatar,
      'email': email,
    };
  }
}

/// 社交互动数据模型
class SocialInteraction {
  final int likes; // 点赞数
  final int thanks; // 感谢数
  final List<String> tags; // 标签列表
  final List<UserInfo> transferChain; // 传播链
  final Map<String, int> reactions; // 表情反应 {"👍": 5, "❤️": 2}
  final List<String> badges; // 关系徽章 ["好友", "常客"]
  final DateTime? lastInteractionAt; // 最后互动时间

  SocialInteraction({
    this.likes = 0,
    this.thanks = 0,
    this.tags = const [],
    this.transferChain = const [],
    this.reactions = const {},
    this.badges = const [],
    this.lastInteractionAt,
  });

  factory SocialInteraction.fromJson(Map<String, dynamic> json) {
    return SocialInteraction(
      likes: json['likes'] ?? 0,
      thanks: json['thanks'] ?? 0,
      tags: List<String>.from(json['tags'] ?? []),
      transferChain: (json['transferChain'] as List<dynamic>? ?? []).map((user) => UserInfo.fromJson(user)).toList(),
      reactions: Map<String, int>.from(json['reactions'] ?? {}),
      badges: List<String>.from(json['badges'] ?? []),
      lastInteractionAt: json['lastInteractionAt'] != null ? DateTime.parse(json['lastInteractionAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'likes': likes,
      'thanks': thanks,
      'tags': tags,
      'transferChain': transferChain.map((user) => user.toJson()).toList(),
      'reactions': reactions,
      'badges': badges,
      'lastInteractionAt': lastInteractionAt?.toIso8601String(),
    };
  }

  /// 获取总互动数
  int get totalInteractions => likes + thanks + reactions.values.fold(0, (sum, count) => sum + count);

  /// 获取传播链文本
  String get transferChainText {
    if (transferChain.isEmpty) return '';
    return transferChain.map((user) => user.name).join(' → ');
  }

  SocialInteraction copyWith({
    int? likes,
    int? thanks,
    List<String>? tags,
    List<UserInfo>? transferChain,
    Map<String, int>? reactions,
    List<String>? badges,
    DateTime? lastInteractionAt,
  }) {
    return SocialInteraction(
      likes: likes ?? this.likes,
      thanks: thanks ?? this.thanks,
      tags: tags ?? this.tags,
      transferChain: transferChain ?? this.transferChain,
      reactions: reactions ?? this.reactions,
      badges: badges ?? this.badges,
      lastInteractionAt: lastInteractionAt ?? this.lastInteractionAt,
    );
  }
}

/// 记录模型
class RecordModel {
  final String id;
  final RecordType type;
  final RecordStatus status;
  final String title;
  final String? description;
  final List<FileInfo> files;
  final UserInfo? sender;
  final List<UserInfo> recipients;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final DateTime? completedAt;
  final String? shareUrl;
  final int totalDownloads;
  final int totalViews;
  final Map<String, dynamic>? metadata;
  final SocialInteraction social; // 新增：社交互动数据

  RecordModel({
    required this.id,
    required this.type,
    required this.status,
    required this.title,
    this.description,
    required this.files,
    this.sender,
    required this.recipients,
    required this.createdAt,
    this.expiresAt,
    this.completedAt,
    this.shareUrl,
    this.totalDownloads = 0,
    this.totalViews = 0,
    this.metadata,
    SocialInteraction? social,
  }) : social = social ?? SocialInteraction();

  factory RecordModel.fromJson(Map<String, dynamic> json) {
    return RecordModel(
      id: json['id'] ?? '',
      type: RecordType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => RecordType.sent,
      ),
      status: RecordStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => RecordStatus.active,
      ),
      title: json['title'] ?? '',
      description: json['description'],
      files: (json['files'] as List<dynamic>? ?? []).map((file) => FileInfo.fromJson(file)).toList(),
      sender: json['sender'] != null ? UserInfo.fromJson(json['sender']) : null,
      recipients: (json['recipients'] as List<dynamic>? ?? []).map((user) => UserInfo.fromJson(user)).toList(),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt']) : null,
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      shareUrl: json['shareUrl'],
      totalDownloads: json['totalDownloads'] ?? 0,
      totalViews: json['totalViews'] ?? 0,
      metadata: json['metadata'],
      social: json['social'] != null ? SocialInteraction.fromJson(json['social']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'status': status.name,
      'title': title,
      'description': description,
      'files': files.map((file) => file.toJson()).toList(),
      'sender': sender?.toJson(),
      'recipients': recipients.map((user) => user.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'shareUrl': shareUrl,
      'totalDownloads': totalDownloads,
      'totalViews': totalViews,
      'metadata': metadata,
      'social': social.toJson(),
    };
  }

  /// 是否已过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// 剩余时间
  Duration? get remainingTime {
    if (expiresAt == null) return null;
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  /// 格式化剩余时间
  String get formattedRemainingTime {
    final remaining = remainingTime;
    if (remaining == null) return '永久有效';
    if (remaining == Duration.zero) return '已过期';

    final days = remaining.inDays;
    final hours = remaining.inHours % 24;
    final minutes = remaining.inMinutes % 60;

    if (days > 0) return '$days天$hours小时';
    if (hours > 0) return '$hours小时$minutes分钟';
    return '$minutes分钟';
  }

  /// 获取主要文件（第一个文件或最大的文件）
  FileInfo? get primaryFile {
    if (files.isEmpty) return null;
    if (files.length == 1) return files.first;

    // 返回最大的文件
    return files.reduce((a, b) => a.size > b.size ? a : b);
  }

  /// 总文件大小
  int get totalSize {
    return files.fold(0, (sum, file) => sum + file.size);
  }

  /// 格式化总文件大小
  String get formattedTotalSize {
    final size = totalSize;
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    if (size < 1024 * 1024 * 1024) return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// 复制记录（用于重新发送等操作）
  RecordModel copyWith({
    String? id,
    RecordType? type,
    RecordStatus? status,
    String? title,
    String? description,
    List<FileInfo>? files,
    UserInfo? sender,
    List<UserInfo>? recipients,
    DateTime? createdAt,
    DateTime? expiresAt,
    DateTime? completedAt,
    String? shareUrl,
    int? totalDownloads,
    int? totalViews,
    Map<String, dynamic>? metadata,
    SocialInteraction? social,
  }) {
    return RecordModel(
      id: id ?? this.id,
      type: type ?? this.type,
      status: status ?? this.status,
      title: title ?? this.title,
      description: description ?? this.description,
      files: files ?? this.files,
      sender: sender ?? this.sender,
      recipients: recipients ?? this.recipients,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      completedAt: completedAt ?? this.completedAt,
      shareUrl: shareUrl ?? this.shareUrl,
      totalDownloads: totalDownloads ?? this.totalDownloads,
      totalViews: totalViews ?? this.totalViews,
      metadata: metadata ?? this.metadata,
      social: social ?? this.social,
    );
  }
}

/// 记录类型扩展
extension RecordTypeExtension on RecordType {
  /// 获取显示名称
  String get displayName {
    switch (this) {
      case RecordType.sent:
        return '我发送的';
      case RecordType.received:
        return '发给我的';
      case RecordType.collected:
        return '我收集的';
      case RecordType.collecting:
        return '向我收集的';
    }
  }

  /// 获取图标
  String get icon {
    switch (this) {
      case RecordType.sent:
        return '📤';
      case RecordType.received:
        return '📥';
      case RecordType.collected:
        return '📋';
      case RecordType.collecting:
        return '📝';
    }
  }
}

/// 记录状态扩展
extension RecordStatusExtension on RecordStatus {
  /// 获取显示名称
  String get displayName {
    switch (this) {
      case RecordStatus.active:
        return '进行中';
      case RecordStatus.completed:
        return '已完成';
      case RecordStatus.expired:
        return '已过期';
      case RecordStatus.cancelled:
        return '已取消';
    }
  }

  /// 获取颜色（主题相关，在UI层处理）
  String get colorKey {
    switch (this) {
      case RecordStatus.active:
        return 'primary';
      case RecordStatus.completed:
        return 'success';
      case RecordStatus.expired:
        return 'error';
      case RecordStatus.cancelled:
        return 'outline';
    }
  }
}
