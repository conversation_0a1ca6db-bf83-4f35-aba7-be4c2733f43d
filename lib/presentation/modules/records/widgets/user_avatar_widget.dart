import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/record_model.dart';
import '../../../../core/theme/theme_helper.dart';

/// 用户头像组件
/// 支持网络头像、默认头像和在线状态指示器
class UserAvatarWidget extends StatelessWidget {
  /// 用户信息
  final UserInfo? user;

  /// 头像尺寸
  final double size;

  /// 是否显示在线状态
  final bool showOnlineStatus;

  /// 边框宽度
  final double borderWidth;

  /// 在线状态指示器尺寸
  final double onlineIndicatorSize;

  const UserAvatarWidget({
    super.key,
    this.user,
    this.size = 36.0,
    this.showOnlineStatus = true,
    this.borderWidth = 2.5,
    this.onlineIndicatorSize = 10.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return _buildAvatarContainer(
      theme: theme,
      child: user?.avatar != null ? _buildNetworkAvatar(user!.avatar!, theme) : _buildDefaultAvatar(theme),
      showOnlineStatus: showOnlineStatus && user != null,
    );
  }

  /// 构建头像容器（统一样式）
  Widget _buildAvatarContainer({
    required AppTheme theme,
    required Widget child,
    bool showOnlineStatus = false,
  }) {
    final avatar = Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: theme.primary,
        border: Border.all(
          color: theme.surface,
          width: borderWidth,
        ),
      ),
      child: ClipOval(child: child),
    );

    if (!showOnlineStatus) return avatar;

    return Stack(
      children: [
        avatar,
        _buildOnlineStatusIndicator(theme),
      ],
    );
  }

  /// 构建在线状态指示器
  Widget _buildOnlineStatusIndicator(AppTheme theme) {
    return Positioned(
      right: -1,
      bottom: -1,
      child: Container(
        width: onlineIndicatorSize,
        height: onlineIndicatorSize,
        decoration: BoxDecoration(
          color: Colors.green,
          shape: BoxShape.circle,
          border: Border.all(
            color: theme.surface,
            width: 2,
          ),
        ),
      ),
    );
  }

  /// 构建网络头像
  Widget _buildNetworkAvatar(String avatarUrl, AppTheme theme) {
    return Image.network(
      avatarUrl,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(theme),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar(AppTheme theme) {
    return Icon(
      LucideIcons.user,
      color: theme.onPrimary,
      size: size * 0.5, // 图标大小为头像尺寸的一半
    );
  }
}

/// 记录卡片专用的用户头像组件
/// 根据记录类型自动获取对应的用户信息
class RecordUserAvatarWidget extends StatelessWidget {
  /// 记录模型
  final RecordModel record;

  /// 头像尺寸
  final double size;

  /// 是否显示在线状态
  final bool showOnlineStatus;

  const RecordUserAvatarWidget({
    super.key,
    required this.record,
    this.size = 36.0,
    this.showOnlineStatus = true,
  });

  @override
  Widget build(BuildContext context) {
    final user = _getCurrentUser();

    return UserAvatarWidget(
      user: user,
      size: size,
      showOnlineStatus: showOnlineStatus,
    );
  }

  /// 获取当前记录对应的用户信息
  UserInfo? _getCurrentUser() {
    switch (record.type) {
      case RecordType.sent:
      case RecordType.collected:
        return record.recipients.isNotEmpty ? record.recipients.first : null;
      case RecordType.received:
      case RecordType.collecting:
        return record.sender;
    }
  }
}
