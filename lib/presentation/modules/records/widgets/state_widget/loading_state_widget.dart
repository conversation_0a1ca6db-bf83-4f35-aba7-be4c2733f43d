import 'package:flutter/material.dart';
import '../../../../../core/theme/theme_helper.dart';

/// 加载状态组件
class LoadingStateWidget extends StatelessWidget {
  const LoadingStateWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: theme.primary,
          ),
          SizedBox(height: AppTheme.spacingVertical16),
          Text(
            '加载中...',
            style: theme.getTextStyle(
              color: theme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }
}
