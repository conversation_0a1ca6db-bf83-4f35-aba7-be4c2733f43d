import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../../models/record_model.dart';
import '../../../../../core/theme/theme_helper.dart';

/// 空状态组件
class EmptyStateWidget extends StatelessWidget {
  final RecordType type;

  const EmptyStateWidget({
    super.key,
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    String title;
    String subtitle;
    IconData icon;
    String actionText;
    VoidCallback? onAction;

    switch (type) {
      case RecordType.sent:
        title = '还没有发送记录';
        subtitle = '开始发送文件给朋友吧';
        icon = LucideIcons.upload;
        actionText = '去发送';
        onAction = () => Get.toNamed('/send');
        break;
      case RecordType.received:
        title = '还没有接收记录';
        subtitle = '等待朋友发送文件给您';
        icon = LucideIcons.download;
        actionText = '分享接收链接';
        onAction = null; // 暂时没有实现
        break;
      case RecordType.collected:
        title = '还没有收集记录';
        subtitle = '创建收集任务来收集文件';
        icon = LucideIcons.folderOpen;
        actionText = '去收集';
        onAction = () => Get.toNamed('/collect');
        break;
      case RecordType.collecting:
        title = '还没有收集请求';
        subtitle = '等待朋友向您发起收集请求';
        icon = LucideIcons.folderOpen;
        actionText = '了解收集功能';
        onAction = null; // 暂时没有实现
        break;
    }

    return Center(
      child: Padding(
        padding: AppTheme.paddingAll12,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: theme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 40,
                color: theme.primary.withValues(alpha: 0.6),
              ),
            ),
            SizedBox(height: AppTheme.spacingVertical24),
            // 标题
            Text(
              title,
              style: theme.getTextStyle(
                fontWeight: FontWeight.w600,
                color: theme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppTheme.spacingVertical8),
            // 副标题
            Text(
              subtitle,
              style: theme.getTextStyle(
                color: theme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
            if (onAction != null) ...[
              SizedBox(height: AppTheme.spacingVertical24),
              // 操作按钮
              ElevatedButton(
                onPressed: onAction,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingHorizontal24,
                    vertical: AppTheme.spacingVertical12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusValue12),
                  ),
                ),
                child: Text(
                  actionText,
                  style: theme.getTextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
