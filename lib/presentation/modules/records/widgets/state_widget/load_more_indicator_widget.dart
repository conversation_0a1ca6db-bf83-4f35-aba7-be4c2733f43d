import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/records_controller.dart';
import '../../../../../core/theme/theme_helper.dart';

/// 加载更多指示器组件
class LoadMoreIndicatorWidget extends GetWidget<RecordsController> {
  const LoadMoreIndicatorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Obx(() => controller.isLoading.value
        ? Container(
            padding: EdgeInsets.all(AppTheme.spacingVertical16),
            alignment: Alignment.center,
            child: SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: theme.primary,
              ),
            ),
          )
        : const SizedBox.shrink());
  }
}
