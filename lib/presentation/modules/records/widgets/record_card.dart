import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:nicexfer_app/presentation/global_widgets/chips/unified_filter_chip.dart';
import '../models/record_model.dart';
import '../../../../core/theme/theme_helper.dart';
import 'user_avatar_widget.dart';
import 'sheets/record_action_sheet.dart';
import 'dialog/record_delete_dialog.dart';

class RecordCard extends StatefulWidget {
  final RecordModel record;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;
  final VoidCallback? onShare;
  final VoidCallback? onCopyLink;
  final VoidCallback? onReshare;

  const RecordCard({
    super.key,
    required this.record,
    this.onTap,
    this.onDelete,
    this.onShare,
    this.onCopyLink,
    this.onReshare,
  });

  @override
  State<RecordCard> createState() => _RecordCardState();
}

class _RecordCardState extends State<RecordCard> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  RecordModel get record => widget.record;
  VoidCallback? get onTap => widget.onTap;
  VoidCallback? get onDelete => widget.onDelete;
  VoidCallback? get onShare => widget.onShare;
  VoidCallback? get onCopyLink => widget.onCopyLink;
  VoidCallback? get onReshare => widget.onReshare;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacingVertical12),
      child: Container(
        decoration: BoxDecoration(
          color: theme.surface,
          borderRadius: BorderRadius.circular(AppTheme.spacingHorizontal12),
          border: Border.all(
            color: theme.outline.withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              onTap?.call();
              // 添加触觉反馈（移动端）
              HapticFeedback.lightImpact();
            },
            onLongPress: () {
              _showActionSheet();
              // 添加触觉反馈（移动端）
              HapticFeedback.mediumImpact();
            },
            borderRadius: BorderRadius.circular(AppTheme.spacingHorizontal12),
            child: Padding(
              padding: AppTheme.paddingAll12,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildHeader(theme),
                  SizedBox(height: AppTheme.spacingVertical12),
                  _buildContent(theme),
                  SizedBox(height: AppTheme.spacingVertical12),
                  _buildFooter(theme),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建头部（用户信息、状态和操作按钮）
  Widget _buildHeader(AppTheme theme) {
    return Row(
      children: [
        // 用户头像和在线状态
        _buildUserAvatar(theme),
        SizedBox(width: AppTheme.spacingHorizontal8),
        // 状态标签
        _buildStatusChip(theme),
        const Spacer(),
        // 快速操作按钮
        _buildQuickActions(theme),
        // 更多操作按钮
        _buildActionButton(theme),
      ],
    );
  }

  /// 构建用户头像和在线状态
  Widget _buildUserAvatar(AppTheme theme) {
    return RecordUserAvatarWidget(
      record: record,
      size: 36.0,
      showOnlineStatus: true,
    );
  }

  /// 构建快速操作按钮
  Widget _buildQuickActions(AppTheme theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 快速分享按钮
        if (record.shareUrl != null)
          _buildQuickActionButton(
            icon: LucideIcons.share,
            onTap: onShare,
            theme: theme,
          ),
        // 快速复制链接按钮
        if (record.shareUrl != null)
          _buildQuickActionButton(
            icon: LucideIcons.link,
            onTap: onCopyLink,
            theme: theme,
          ),
      ],
    );
  }

  /// 构建快速操作按钮
  Widget _buildQuickActionButton({
    required IconData icon,
    required VoidCallback? onTap,
    required AppTheme theme,
  }) {
    return Padding(
      padding: EdgeInsets.only(right: AppTheme.spacingHorizontal4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusValue8),
          child: Container(
            padding: EdgeInsets.all(AppTheme.padding8),
            child: Icon(
              icon,
              size: 16,
              color: theme.primary.withValues(alpha: 0.7),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建状态标签
  Widget _buildStatusChip(AppTheme theme) {
    Color statusColor;
    IconData statusIcon;

    switch (record.status) {
      case RecordStatus.active:
        statusColor = theme.primary;
        statusIcon = LucideIcons.circlePlay;
        break;
      case RecordStatus.completed:
        statusColor = Colors.green.shade700;
        statusIcon = LucideIcons.circleCheck;
        break;
      case RecordStatus.expired:
        statusColor = theme.error;
        statusIcon = LucideIcons.clock;
        break;
      case RecordStatus.cancelled:
        statusColor = theme.outline;
        statusIcon = LucideIcons.circleX;
        break;
    }

    return UnifiedFilterChip.modern(
      isSelected: true,
      customColor: statusColor,
      label: record.status.displayName,
      customPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      customBorderRadius: 12,
      icon: statusIcon,
      onTap: () {
        onTap?.call();
      },
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton(AppTheme theme) {
    return IconButton(
      onPressed: _showActionSheet,
      icon: Icon(
        LucideIcons.ellipsisVertical,
        color: theme.onSurface.withValues(alpha: 0.6),
        size: 20,
      ),
      constraints: const BoxConstraints(
        minWidth: 32,
        minHeight: 32,
      ),
      padding: EdgeInsets.zero,
    );
  }

  /// 构建内容区域
  Widget _buildContent(AppTheme theme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 文件图标或缩略图
        _buildFileIcon(theme),
        SizedBox(width: AppTheme.spacingHorizontal12),
        // 文件信息
        Expanded(
          child: _buildFileInfo(theme),
        ),
      ],
    );
  }

  /// 构建文件图标
  Widget _buildFileIcon(AppTheme theme) {
    final primaryFile = record.primaryFile;

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: theme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusValue12),
      ),
      child: primaryFile?.thumbnailUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusValue12),
              child: Image.network(
                primaryFile!.thumbnailUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultIcon(theme),
              ),
            )
          : _buildDefaultIcon(theme),
    );
  }

  /// 构建默认图标
  Widget _buildDefaultIcon(AppTheme theme) {
    IconData iconData;
    switch (record.type) {
      case RecordType.sent:
        iconData = LucideIcons.upload;
        break;
      case RecordType.received:
        iconData = LucideIcons.download;
        break;
      case RecordType.collected:
        iconData = LucideIcons.folderOpen;
        break;
      case RecordType.collecting:
        iconData = LucideIcons.combine;
        break;
    }

    return Icon(
      iconData,
      color: theme.primary,
      size: 24,
    );
  }

  /// 构建文件信息
  Widget _buildFileInfo(AppTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 标题和故事标签（强化层次）
        Row(
          children: [
            Expanded(
              child: Text(
                record.title,
                style: theme.getTextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 18,
                  color: theme.onSurface,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            _buildStoryIndicator(theme),
          ],
        ),
        const SizedBox(height: 8),
        // 故事性描述
        _buildStoryDescription(theme),
        const SizedBox(height: 6),
        // 文件统计信息
        _buildFileStats(theme),
        const SizedBox(height: 8),
        // 交互时间线
        _buildInteractionTimeline(theme),
      ],
    );
  }

  /// 构建故事指示器
  Widget _buildStoryIndicator(AppTheme theme) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppTheme.spacingHorizontal8,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.primary.withValues(alpha: 0.2),
            theme.secondary.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusValue8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            LucideIcons.book,
            size: 10,
            color: theme.primary.withValues(alpha: 0.8),
          ),
          SizedBox(width: 2),
          Text(
            '故事',
            style: theme.getTextStyle(
              fontSize: 10,
              color: theme.primary.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建故事性描述
  Widget _buildStoryDescription(AppTheme theme) {
    String storyText = '';

    switch (record.type) {
      case RecordType.sent:
        if (record.recipients.isNotEmpty) {
          storyText = '分享了珍贵的文件给 ${record.recipients.first.name}';
          if (record.recipients.length > 1) {
            storyText += ' 等${record.recipients.length}位朋友';
          }
        }
        break;
      case RecordType.received:
        if (record.sender != null) {
          storyText = '收到了来自 ${record.sender!.name} 的温暖分享';
        }
        break;
      case RecordType.collected:
        storyText = '成功收集了 ${record.recipients.length}位朋友的回忆';
        break;
      case RecordType.collecting:
        if (record.sender != null) {
          storyText = '${record.sender!.name} 邀请您分享美好时光';
        }
        break;
    }

    if (record.description?.isNotEmpty == true) {
      storyText = record.description!;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          storyText,
          style: theme
              .getTextStyle(
                color: theme.onSurface.withValues(alpha: 0.7),
                fontSize: 13,
                fontWeight: FontWeight.w400,
              )
              .copyWith(fontStyle: FontStyle.italic),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        if (record.social.tags.isNotEmpty) SizedBox(height: 4),
        if (record.social.tags.isNotEmpty) _buildTagsRow(theme),
      ],
    );
  }

  /// 构建标签行
  Widget _buildTagsRow(AppTheme theme) {
    return Wrap(
      spacing: 4,
      runSpacing: 2,
      children: record.social.tags.map((tag) => _buildTag(tag, theme)).toList(),
    );
  }

  /// 构建单个标签（弱化视觉权重）
  Widget _buildTag(String tag, AppTheme theme) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5, vertical: 1),
      decoration: BoxDecoration(
        color: theme.secondary.withValues(alpha: 0.06),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: theme.secondary.withValues(alpha: 0.2),
          width: 0.5,
        ),
      ),
      child: Text(
        '#$tag',
        style: theme.getTextStyle(
          fontSize: 9,
          color: theme.secondary.withValues(alpha: 0.6),
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  /// 构建交互时间线
  Widget _buildInteractionTimeline(AppTheme theme) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppTheme.spacingHorizontal8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: theme.surfaceContainer.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusValue8),
        border: Border.all(
          color: theme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Icon(
            LucideIcons.activity,
            size: 12,
            color: theme.primary.withValues(alpha: 0.7),
          ),
          SizedBox(width: AppTheme.spacingHorizontal4),
          Expanded(
            child: Text(
              _getTimelineText(),
              style: theme.getTextStyle(
                fontSize: 11,
                color: theme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ),
          // 移除交互指示器，避免与底部统计信息重复
        ],
      ),
    );
  }

  /// 获取时间线文本
  String _getTimelineText() {
    final now = DateTime.now();
    final diff = now.difference(record.createdAt);

    if (diff.inMinutes < 1) {
      return '刚刚创建';
    } else if (diff.inHours < 1) {
      return '${diff.inMinutes}分钟前创建';
    } else if (diff.inDays < 1) {
      return '${diff.inHours}小时前创建';
    } else {
      return '${diff.inDays}天前创建';
    }
  }

  /// 构建文件统计信息
  Widget _buildFileStats(AppTheme theme) {
    return Text(
      '${record.files.length}个文件 • ${record.formattedTotalSize}',
      style: theme.getTextStyle(
        color: theme.onSurface.withValues(alpha: 0.7),
      ),
    );
  }

  /// 构建底部信息
  Widget _buildFooter(AppTheme theme) {
    return Row(
      children: [
        // 移除重复的时间信息，只保留交互时间线中的时间显示
        const Spacer(),
        // 增强的统计信息
        _buildEnhancedStats(theme),
      ],
    );
  }

  /// 构建增强的统计信息
  Widget _buildEnhancedStats(AppTheme theme) {
    final List<Widget> stats = [];

    // 浏览量
    if (record.totalViews > 0) {
      stats.add(_buildStatItem(
        icon: LucideIcons.eye,
        count: record.totalViews,
        color: theme.primary,
        theme: theme,
      ));
    }

    // 下载量
    if (record.totalDownloads > 0) {
      stats.add(_buildStatItem(
        icon: LucideIcons.download,
        count: record.totalDownloads,
        color: theme.secondary,
        theme: theme,
      ));
    }

    // 点赞数
    if (record.social.likes > 0) {
      stats.add(_buildStatItem(
        icon: LucideIcons.thumbsUp,
        count: record.social.likes,
        color: theme.tertiary,
        theme: theme,
      ));
    }

    // 感谢数
    if (record.social.thanks > 0) {
      stats.add(_buildStatItem(
        icon: LucideIcons.heart,
        count: record.social.thanks,
        color: theme.outline,
        theme: theme,
      ));
    }

    // 表情反应（显示最多的表情）
    if (record.social.reactions.isNotEmpty) {
      final topReaction = record.social.reactions.entries.reduce((a, b) => a.value > b.value ? a : b);
      stats.add(_buildEmojiStatItem(
        emoji: topReaction.key,
        count: topReaction.value,
        theme: theme,
      ));
    }

    // 传输链统计（只在有多个传播节点时显示）
    if (record.social.transferChain.length > 1) {
      stats.add(_buildStatItem(
        icon: LucideIcons.trendingUp,
        count: record.social.transferChain.length,
        color: theme.tertiary,
        theme: theme,
        label: '级',
      ));
    }

    // 如果没有任何统计数据，显示"新"
    if (stats.isEmpty) {
      stats.add(_buildStatItem(
        icon: LucideIcons.sparkles,
        count: 0,
        color: theme.primary,
        theme: theme,
        label: '新',
      ));
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: stats
          .expand((widget) => [widget, SizedBox(width: AppTheme.spacingHorizontal8)])
          .take(stats.length * 2 - 1) // 移除最后一个间距
          .toList(),
    );
  }

  /// 构建统计项目（弱化视觉权重）
  Widget _buildStatItem({
    required IconData icon,
    required int count,
    required Color color,
    required AppTheme theme,
    String? label,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppTheme.spacingHorizontal8,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.08),
            color.withValues(alpha: 0.03),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusValue8),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 9,
            color: color.withValues(alpha: 0.6),
          ),
          SizedBox(width: 2),
          Text(
            label ?? count.toString(),
            style: theme.getTextStyle(
              color: color.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
              fontSize: 9,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建表情统计项目
  Widget _buildEmojiStatItem({
    required String emoji,
    required int count,
    required AppTheme theme,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppTheme.spacingHorizontal8,
        vertical: 3,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.primary.withValues(alpha: 0.15),
            theme.primary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusValue8),
        border: Border.all(
          color: theme.primary.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            emoji,
            style: TextStyle(fontSize: 10),
          ),
          SizedBox(width: 3),
          Text(
            count.toString(),
            style: theme.getTextStyle(
              color: theme.primary.withValues(alpha: 0.9),
              fontWeight: FontWeight.w600,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示操作菜单
  void _showActionSheet() {
    RecordActionSheet.show(
      context: context,
      record: record,
      onViewDetails: onTap,
      onCopyLink: onCopyLink,
      onReshare: onReshare,
      onDelete: _showDeleteConfirmation,
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmation() {
    RecordDeleteDialog.show(
      context: context,
      record: record,
      onConfirm: onDelete,
    );
  }
}
