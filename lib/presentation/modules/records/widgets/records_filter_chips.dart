import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../../../global_widgets/chips/unified_filter_chip.dart';
import '../controllers/records_controller.dart';
import '../models/record_model.dart';

/// Records页面的筛选标签组件
class RecordsFilterChips extends GetWidget<RecordsController> {
  const RecordsFilterChips({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildFilterChips();
  }

  /// 构建筛选标签区域
  Widget _buildFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Row(
        children: [
          Obx(() => UnifiedFilterChip.modern(
                label: '全部',
                isSelected: controller.selectedStatus.value == null,
                onTap: () => controller.filterByStatus(null),
                icon: LucideIcons.layoutDashboard,
              )),
          const SizedBox(width: 8),
          ...RecordStatus.values.map((status) => Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Obx(() => UnifiedFilterChip.modern(
                      label: status.displayName,
                      isSelected: controller.selectedStatus.value == status,
                      onTap: () => controller.filterByStatus(status),
                      icon: _getStatusIcon(status),
                    )),
              )),
        ],
      ),
    );
  }

  /// 获取状态图标
  IconData _getStatusIcon(RecordStatus status) {
    switch (status) {
      case RecordStatus.active:
        return LucideIcons.circlePlay;
      case RecordStatus.completed:
        return LucideIcons.circleCheck;
      case RecordStatus.expired:
        return LucideIcons.clock;
      case RecordStatus.cancelled:
        return LucideIcons.circleX;
    }
  }
}
