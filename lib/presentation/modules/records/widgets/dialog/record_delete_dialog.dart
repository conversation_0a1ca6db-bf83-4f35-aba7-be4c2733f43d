import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../../../../../core/theme/theme_helper.dart';
import '../../models/record_model.dart';
import '../../../../global_widgets/dialogs/base_confirm_dialog.dart';
import '../../../../global_widgets/dialogs/confirm_dialog_header.dart';
import '../../../../global_widgets/dialogs/danger_warning.dart';

/// 记录删除确认对话框组件
class RecordDeleteDialog {
  /// 显示删除确认对话框
  static void show({
    required BuildContext context,
    required RecordModel record,
    VoidCallback? onConfirm,
  }) {
    final theme = AppTheme.of(context);
    BaseConfirmDialog.show(
      context: context,
      theme: theme,
      header: ConfirmDialogHeader(
        icon: LucideIcons.trash,
        title: '确认删除',
        subtitle: '此操作无法撤销',
        iconColor: Colors.red,
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        theme: theme,
      ),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.onSurface.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Text(
              '确定要删除「${record.title}」吗？删除后将无法恢复，请谨慎操作。',
              style: theme.getTextStyle(
                fontSize: 15,
                color: theme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
          const SizedBox(height: 16),
          DangerWarning(
            message: '此操作不可逆，请确认后再执行',
            theme: theme,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: theme.surface,
            side: BorderSide(
              color: theme.onSurface.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Text(
            '取消',
            style: theme.getTextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: theme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
            onConfirm?.call();
            HapticFeedback.heavyImpact();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            elevation: 0,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            shadowColor: Colors.red.withValues(alpha: 0.3),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                LucideIcons.trash,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                '确认删除',
                style: theme.getTextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
      barrierDismissible: true,
    );
  }
}