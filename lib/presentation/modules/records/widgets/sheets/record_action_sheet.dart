import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../../../../../core/theme/theme_helper.dart';
import '../../models/record_model.dart';
import '../../../../global_widgets/sheet_base/base_bottom_sheet.dart';
import '../../../../global_widgets/sheet_base/bottom_sheet_header.dart';
import '../../../../global_widgets/sheet_base/bottom_sheet_option_item.dart';

/// 记录操作菜单组件
class RecordActionSheet {
  /// 显示记录操作菜单
  static void show({
    required BuildContext context,
    required RecordModel record,
    VoidCallback? onViewDetails,
    VoidCallback? onCopyLink,
    VoidCallback? onReshare,
    VoidCallback? onDelete,
  }) {
    final theme = AppTheme.of(context);
    BaseBottomSheet.show(
      context,
      BaseBottomSheet(
        theme: theme,
        header: BottomSheetHeader(
          theme: theme,
          icon: LucideIcons.fileText,
          title: record.title,
          subtitle: '选择要执行的操作',
          iconColor: theme.primary,
          backgroundColor: theme.primary,
        ),
        children: [
          BottomSheetOptionItem(
            theme: theme,
            icon: LucideIcons.eye,
            title: '查看详情',
            description: '查看记录的详细信息',
            onTap: () {
              Get.back();
              onViewDetails?.call();
            },
          ),
          const SizedBox(height: 4),
          if (record.shareUrl != null)
            BottomSheetOptionItem(
              theme: theme,
              icon: LucideIcons.link,
              title: '复制链接',
              description: '复制分享链接到剪贴板',
              onTap: () {
                Get.back();
                onCopyLink?.call();
              },
            ),
          const SizedBox(height: 4),
          if (record.type == RecordType.sent || record.type == RecordType.collected)
            BottomSheetOptionItem(
              theme: theme,
              icon: LucideIcons.share,
              title: '重新分享',
              description: '重新生成分享链接',
              onTap: () {
                Get.back();
                onReshare?.call();
              },
            ),
          const SizedBox(height: 4),
          BottomSheetOptionItem(
            theme: theme,
            icon: LucideIcons.trash,
            title: '删除',
            description: '永久删除此记录',
            isDestructive: true,
            onTap: () {
              Get.back();
              onDelete?.call();
            },
          ),
        ],
      ),
    );
  }
}