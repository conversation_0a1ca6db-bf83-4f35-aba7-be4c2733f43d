import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../../../../core/theme/theme_helper.dart';
import '../controllers/records_controller.dart';

/// Records页面的TabBar组件
class RecordsTabBar extends GetWidget<RecordsController> {
  const RecordsTabBar({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    return _buildMaterialTabBar(theme);
  }

  /// 构建Material TabBar
  Widget _buildMaterialTabBar(AppTheme theme) {
    return Container(
      height: 56,
      margin: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: theme.isDarkMode ? theme.surfaceContainer.withValues(alpha: 0.5) : theme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadow.withValues(alpha: 0.06),
            blurRadius: 12,
            offset: Offset(0, 4),
          ),
          BoxShadow(
            color: theme.shadow.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Obx(
        () => TabBar(
          controller: controller.tabController,
          onTap: (index) => controller.changeTab(index),
          tabs: [
            _buildStyledTab(
              selectedIcon: LucideIcons.send,
              unselectedIcon: LucideIcons.sendToBack,
              text: '我发送的',
              theme: theme,
              isSelected: controller.currentTabIndex.value == 0,
            ),
            _buildStyledTab(
              selectedIcon: LucideIcons.fileDown,
              unselectedIcon: LucideIcons.download,
              text: '发给我的',
              theme: theme,
              isSelected: controller.currentTabIndex.value == 1,
            ),
            _buildStyledTab(
              selectedIcon: LucideIcons.combine,
              unselectedIcon: LucideIcons.boxes,
              text: '我收集的',
              theme: theme,
              isSelected: controller.currentTabIndex.value == 2,
            ),
            _buildStyledTab(
              selectedIcon: LucideIcons.users,
              unselectedIcon: LucideIcons.userPlus,
              text: '向我收集',
              theme: theme,
              isSelected: controller.currentTabIndex.value == 3,
            ),
          ],
          labelColor: theme.primaryContainer,
          unselectedLabelColor: theme.onSurface.withValues(alpha: 0.7),
          labelStyle: theme.getTextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: theme.getTextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          indicator: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                theme.primary,
                theme.primary.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: theme.primary.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          indicatorSize: TabBarIndicatorSize.tab,
          indicatorPadding: EdgeInsets.all(4),
          dividerColor: Colors.transparent,
          splashFactory: InkRipple.splashFactory,
          splashBorderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  /// 构建样式化的Tab项
  Widget _buildStyledTab({
    required IconData selectedIcon,
    required IconData unselectedIcon,
    required String text,
    required AppTheme theme,
    required bool isSelected,
  }) {
    return SizedBox(
      height: 48,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isSelected ? selectedIcon : unselectedIcon,
            size: 20,
          ),
          SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
