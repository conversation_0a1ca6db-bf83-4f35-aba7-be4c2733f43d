import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';

import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 文件图标组件
/// 用于显示文件类型对应的图标
class FileIcon extends StatelessWidget {
  /// 文件名
  final String fileName;

  /// 是否为文件夹
  final bool isFolder;

  /// 应用主题
  final AppTheme theme;

  /// 图标大小
  final double iconSize;

  /// 容器大小
  final double? containerSize;

  /// 是否显示渐变背景
  final bool showGradient;

  /// 是否显示阴影
  final bool showShadow;

  /// 边框圆角
  final double borderRadius;

  /// 自定义图标
  final IconData? customIcon;

  /// 自定义颜色
  final List<Color>? customColors;

  const FileIcon({
    super.key,
    required this.fileName,
    required this.isFolder,
    required this.theme,
    this.iconSize = 20,
    this.containerSize,
    this.showGradient = true,
    this.showShadow = true,
    this.borderRadius = 10,
    this.customIcon,
    this.customColors,
  });

  @override
  Widget build(BuildContext context) {
    final icon = customIcon ?? _getFileIcon();
    final colors = customColors ?? _getFileIconColors();
    final size = containerSize ?? (iconSize + 20);

    if (!showGradient) {
      return Icon(
        icon,
        size: iconSize,
        color: colors.first,
      );
    }

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: colors,
        ),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: showShadow
            ? [
                BoxShadow(
                  color: colors.first.withValues(alpha: 0.2),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Icon(
        icon,
        color: Colors.white,
        size: iconSize,
      ),
    );
  }

  /// 获取文件图标
  IconData _getFileIcon() {
    if (isFolder) {
      return LucideIcons.folder;
    }

    final extension = _getFileExtension(fileName).toLowerCase();

    switch (extension) {
      case 'pdf':
        return LucideIcons.fileText;
      case 'doc':
      case 'docx':
        return LucideIcons.fileText;
      case 'xls':
      case 'xlsx':
        return LucideIcons.fileSpreadsheet;
      case 'ppt':
      case 'pptx':
        return LucideIcons.presentation;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return LucideIcons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
      case 'webm':
        return LucideIcons.video;
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
      case 'm4a':
        return LucideIcons.music;
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return LucideIcons.archive;
      case 'txt':
      case 'md':
      case 'rtf':
        return LucideIcons.fileText;
      case 'html':
      case 'htm':
      case 'css':
      case 'js':
      case 'json':
      case 'xml':
        return LucideIcons.code;
      default:
        return LucideIcons.file;
    }
  }

  /// 获取文件图标颜色
  List<Color> _getFileIconColors() {
    if (isFolder) {
      return [const Color(0xFF4A90E2), const Color(0xFF357ABD)];
    }

    final extension = _getFileExtension(fileName).toLowerCase();

    switch (extension) {
      case 'pdf':
        return [const Color(0xFFE74C3C), const Color(0xFFC0392B)];
      case 'doc':
      case 'docx':
        return [const Color(0xFF2980B9), const Color(0xFF1F618D)];
      case 'xls':
      case 'xlsx':
        return [const Color(0xFF27AE60), const Color(0xFF1E8449)];
      case 'ppt':
      case 'pptx':
        return [const Color(0xFFE67E22), const Color(0xFFD35400)];
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return [const Color(0xFF9B59B6), const Color(0xFF7D3C98)];
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
      case 'webm':
        return [const Color(0xFFE74C3C), const Color(0xFFC0392B)];
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
      case 'm4a':
        return [const Color(0xFF1ABC9C), const Color(0xFF16A085)];
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return [const Color(0xFF95A5A6), const Color(0xFF7F8C8D)];
      case 'txt':
      case 'md':
      case 'rtf':
        return [const Color(0xFF34495E), const Color(0xFF2C3E50)];
      case 'html':
      case 'htm':
      case 'css':
      case 'js':
      case 'json':
      case 'xml':
        return [const Color(0xFFF39C12), const Color(0xFFE67E22)];
      default:
        return [const Color(0xFF95A5A6), const Color(0xFF7F8C8D)];
    }
  }

  /// 获取文件扩展名
  String _getFileExtension(String fileName) {
    final lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex == -1 || lastDotIndex == fileName.length - 1) {
      return '';
    }
    return fileName.substring(lastDotIndex + 1);
  }
}
