import 'package:flutter/material.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';
import 'package:nicexfer_app/presentation/modules/files/controllers/files_controller.dart';

/// 文件信息卡片组件
class FileInfoCard extends StatelessWidget {
  final FileItem file;
  final AppTheme theme;

  const FileInfoCard({
    super.key,
    required this.file,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.onSurface.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 文件图标
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: _getFileIconColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              _getFileIcon(),
              color: _getFileIconColor(),
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          // 文件信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  file.name,
                  style: theme.getTextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: theme.onSurface,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  _getFileInfo(),
                  style: theme.getTextStyle(
                    fontSize: 14,
                    color: theme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getFileIcon() {
    if (file.isFolder) {
      return Icons.folder_rounded;
    }

    final extension = file.name.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf_rounded;
      case 'doc':
      case 'docx':
        return Icons.description_rounded;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart_rounded;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow_rounded;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return Icons.image_rounded;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return Icons.video_file_rounded;
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
        return Icons.audio_file_rounded;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.archive_rounded;
      case 'txt':
        return Icons.text_snippet_rounded;
      default:
        return Icons.insert_drive_file_rounded;
    }
  }

  Color _getFileIconColor() {
    if (file.isFolder) {
      return const Color(0xFF2196F3); // 蓝色
    }

    final extension = file.name.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return const Color(0xFFE53935); // 红色
      case 'doc':
      case 'docx':
        return const Color(0xFF1976D2); // 深蓝色
      case 'xls':
      case 'xlsx':
        return const Color(0xFF388E3C); // 绿色
      case 'ppt':
      case 'pptx':
        return const Color(0xFFD84315); // 橙红色
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return const Color(0xFF7B1FA2); // 紫色
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return const Color(0xFF5D4037); // 棕色
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
        return const Color(0xFF00796B); // 青色
      case 'zip':
      case 'rar':
      case '7z':
        return const Color(0xFF455A64); // 蓝灰色
      case 'txt':
        return const Color(0xFF616161); // 灰色
      default:
        return const Color(0xFF757575); // 默认灰色
    }
  }

  String _getFileInfo() {
    if (file.isFolder) {
      return '文件夹';
    }

    final extension = file.name.split('.').last.toLowerCase();
    String fileType = _getFileTypeDescription(extension);

    if (file.size != null && file.size! > 0) {
      return '$fileType • ${_formatFileSize(file.size!)}';
    }

    return fileType;
  }

  String _getFileTypeDescription(String extension) {
    switch (extension) {
      case 'pdf':
        return 'PDF 文档';
      case 'doc':
      case 'docx':
        return 'Word 文档';
      case 'xls':
      case 'xlsx':
        return 'Excel 表格';
      case 'ppt':
      case 'pptx':
        return 'PowerPoint 演示文稿';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return '图片文件';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return '视频文件';
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
        return '音频文件';
      case 'zip':
      case 'rar':
      case '7z':
        return '压缩文件';
      case 'txt':
        return '文本文件';
      default:
        return '${extension.toUpperCase()} 文件';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
