import 'package:flutter/material.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 文件来源标签组件
/// 用于显示文件的来源信息
class FileSourceLabel extends StatelessWidget {
  /// 来源文本
  final String source;

  /// 应用主题
  final AppTheme theme;

  /// 是否为预览模式（在预览背景上显示）
  final bool isPreviewMode;

  /// 字体大小
  final double fontSize;

  /// 内边距
  final EdgeInsets padding;

  /// 边框圆角
  final double borderRadius;

  const FileSourceLabel({
    super.key,
    required this.source,
    required this.theme,
    this.isPreviewMode = false,
    this.fontSize = 9,
    this.padding = const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
    this.borderRadius = 3,
  });

  @override
  Widget build(BuildContext context) {
    if (source.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: isPreviewMode ? Colors.black.withValues(alpha: 0.5) : theme.onSurface.withValues(alpha: 0.06),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Text(
        source,
        style: theme.getTextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w500,
          color: isPreviewMode ? Colors.white.withValues(alpha: 0.9) : theme.onSurface.withValues(alpha: 0.6),
        ),
      ),
    );
  }
}
