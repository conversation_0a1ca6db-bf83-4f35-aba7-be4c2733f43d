import 'package:flutter/material.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 文件类型标签组件
/// 用于显示文件的类型标签
class FileTypeLabel extends StatelessWidget {
  /// 文件类型文本
  final String fileType;

  /// 应用主题
  final AppTheme theme;

  /// 是否为预览模式（在预览背景上显示）
  final bool isPreviewMode;

  /// 是否为文件夹
  final bool isFolder;

  /// 字体大小
  final double fontSize;

  /// 内边距
  final EdgeInsets padding;

  /// 边框圆角
  final double borderRadius;

  /// 自定义背景颜色
  final Color? backgroundColor;

  /// 自定义文本颜色
  final Color? textColor;

  const FileTypeLabel({
    super.key,
    required this.fileType,
    required this.theme,
    this.isPreviewMode = false,
    this.isFolder = false,
    this.fontSize = 8,
    this.padding = const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
    this.borderRadius = 3,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    if (fileType.isEmpty) {
      return const SizedBox.shrink();
    }

    final displayText = isFolder ? '文件夹' : fileType.toUpperCase();

    Color bgColor;
    Color txtColor;

    if (backgroundColor != null && textColor != null) {
      bgColor = backgroundColor!;
      txtColor = textColor!;
    } else if (isPreviewMode) {
      bgColor = Colors.white.withValues(alpha: 0.9);
      txtColor = theme.primary;
    } else {
      bgColor = theme.primary.withValues(alpha: 0.1);
      txtColor = theme.primary;
    }

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Text(
        displayText,
        style: theme.getTextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w500,
          color: txtColor,
        ),
      ),
    );
  }
}
