import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';

import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 文件大小显示组件
/// 用于显示文件的大小信息
class FileSizeDisplay extends StatelessWidget {
  /// 文件大小（字节）
  final int size;

  /// 应用主题
  final AppTheme theme;

  /// 是否为预览模式（在预览背景上显示）
  final bool isPreviewMode;

  /// 字体大小
  final double fontSize;

  /// 图标大小
  final double iconSize;

  /// 图标和文本间距
  final double spacing;

  /// 是否显示图标
  final bool showIcon;

  const FileSizeDisplay({
    super.key,
    required this.size,
    required this.theme,
    this.isPreviewMode = false,
    this.fontSize = 10,
    this.iconSize = 10,
    this.spacing = 2,
    this.showIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    if (size <= 0) {
      return const SizedBox.shrink();
    }

    final formattedSize = _formatFileSize(size);
    final textColor = isPreviewMode ? Colors.white.withValues(alpha: 0.8) : theme.onSurface.withValues(alpha: 0.6);
    final iconColor = isPreviewMode ? Colors.white.withValues(alpha: 0.8) : theme.onSurface.withValues(alpha: 0.5);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showIcon) ...[
          Icon(
            LucideIcons.hardDrive,
            size: iconSize,
            color: iconColor,
          ),
          SizedBox(width: spacing),
        ],
        Text(
          formattedSize,
          style: theme.getTextStyle(
            fontSize: fontSize,
            color: textColor,
          ),
        ),
      ],
    );
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }
}
