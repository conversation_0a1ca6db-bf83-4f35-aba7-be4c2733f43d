import 'package:flutter/material.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';
import 'package:nicexfer_app/presentation/global_widgets/sheet_base/bottom_sheet_header.dart';
import '../../../../global_widgets/sheet_base/base_bottom_sheet.dart';
import '../../../../global_widgets/sheet_base/bottom_sheet_option_item.dart';

/// 更多选项底部弹窗组件
class MoreOptionsBottomSheet extends StatelessWidget {
  final AppTheme theme;
  final VoidCallback onUpload;
  final VoidCallback onCreateFolder;
  final VoidCallback onSettings;

  const MoreOptionsBottomSheet({
    super.key,
    required this.theme,
    required this.onUpload,
    required this.onCreateFolder,
    required this.onSettings,
  });

  /// 显示更多选项底部弹窗
  static void show(
    BuildContext context,
    AppTheme theme, {
    required VoidCallback onUpload,
    required VoidCallback onCreateFolder,
    required VoidCallback onSettings,
  }) {
    BaseBottomSheet.show(
      context,
      MoreOptionsBottomSheet(
        theme: theme,
        onUpload: onUpload,
        onCreateFolder: onCreateFolder,
        onSettings: onSettings,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    const availableActionsCount = 4;

    return BaseBottomSheet(
      theme: theme,
      header: BottomSheetHeader(
        theme: theme,
        icon: Icons.construction,
        title: '工具箱',
        subtitle: '$availableActionsCount个可用操作',
        actionHint: '选择要执行的工具',
        iconColor: Colors.orange,
        backgroundColor: Colors.orange,
        showBadge: true,
        badgeText: '$availableActionsCount',
        padding: const EdgeInsets.all(12),
      ),
      children: [
        BottomSheetOptionItem(
          theme: theme,
          icon: Icons.refresh,
          title: '刷新',
          description: '重新加载文件列表',
          onTap: () {
            Navigator.pop(context);
            // 触发刷新逻辑
          },
        ),
        const SizedBox(height: 8),
        BottomSheetOptionItem(
          theme: theme,
          icon: Icons.create_new_folder,
          title: '新建文件夹',
          description: '在当前目录创建新文件夹',
          onTap: () {
            Navigator.pop(context);
            // 触发新建文件夹逻辑
          },
        ),
        const SizedBox(height: 8),
        BottomSheetOptionItem(
          theme: theme,
          icon: Icons.upload_file,
          title: '上传文件',
          description: '从设备选择文件上传',
          onTap: () {
            Navigator.pop(context);
            // 触发上传文件逻辑
          },
        ),
        const SizedBox(height: 8),
        BottomSheetOptionItem(
          theme: theme,
          icon: Icons.settings,
          title: '设置',
          description: '应用设置和偏好',
          onTap: () {
            Navigator.pop(context);
            // 跳转到设置页面
          },
        ),
      ],
    );
  }
}
