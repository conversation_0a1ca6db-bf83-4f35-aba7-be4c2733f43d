import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';
import 'package:nicexfer_app/presentation/global_widgets/sheet_base/bottom_sheet_header.dart';
import 'package:nicexfer_app/presentation/modules/files/widgets/file_sheets/selectable_bottom_sheet_option_item.dart';
import 'package:nicexfer_app/presentation/modules/files/controllers/files_controller.dart';
import 'package:nicexfer_app/presentation/global_widgets/sheet_base/base_bottom_sheet.dart';

import 'toggle_button_group.dart';

/// 排序选项底部弹窗组件
class SortBottomSheet extends StatelessWidget {
  final FilesController controller;
  final AppTheme theme;

  const SortBottomSheet({
    super.key,
    required this.controller,
    required this.theme,
  });

  /// 显示排序选项底部弹窗
  static void show(BuildContext context, FilesController controller, AppTheme theme) {
    BaseBottomSheet.show(
      context,
      SortBottomSheet(
        controller: controller,
        theme: theme,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
      theme: theme,
      header: Obx(() => BottomSheetHeader(
            theme: theme,
            icon: _getSortIcon(controller.sortBy.value),
            title: '排序',
            subtitle: '${_getSortLabel(controller.sortBy.value)} ${controller.isAscending.value ? "↑" : "↓"}',
            actionHint: '轻触选项快速切换',
            iconColor: _getSortColor(controller.sortBy.value),
            backgroundColor: _getSortColor(controller.sortBy.value),
            padding: const EdgeInsets.all(12),
          )),
      children: [
        // 排序选项
        Obx(() => Column(
              children: [
                SelectableBottomSheetOptionItem(
                  theme: theme,
                  icon: Icons.sort_by_alpha,
                  title: '名称',
                  description: '按文件名字母顺序排列',
                  isSelected: controller.sortBy.value == 'name',
                  onTap: () {
                    controller.changeSortBy('name');
                    Navigator.pop(context);
                  },
                ),
                const SizedBox(height: 4),
                SelectableBottomSheetOptionItem(
                  theme: theme,
                  icon: Icons.schedule,
                  title: '修改时间',
                  description: '按最后修改时间排列',
                  isSelected: controller.sortBy.value == 'date',
                  onTap: () {
                    controller.changeSortBy('date');
                    Navigator.pop(context);
                  },
                ),
                const SizedBox(height: 4),
                SelectableBottomSheetOptionItem(
                  theme: theme,
                  icon: Icons.data_usage,
                  title: '文件大小',
                  description: '按文件大小排列',
                  isSelected: controller.sortBy.value == 'size',
                  onTap: () {
                    controller.changeSortBy('size');
                    Navigator.pop(context);
                  },
                ),
              ],
            )),
        const SizedBox(height: 8),
        // 升序降序切换器
        Obx(() => ToggleButtonGroup(
              theme: theme,
              items: [
                ToggleButtonItem(
                  label: '升序',
                  icon: Icons.arrow_upward,
                  isSelected: controller.isAscending.value,
                  onTap: () {
                    if (!controller.isAscending.value) {
                      controller.toggleSortOrder();
                    }
                  },
                ),
                ToggleButtonItem(
                  label: '降序',
                  icon: Icons.arrow_downward,
                  isSelected: !controller.isAscending.value,
                  onTap: () {
                    if (controller.isAscending.value) {
                      controller.toggleSortOrder();
                    }
                  },
                ),
              ],
            )),
      ],
    );
  }

  /// 获取排序类型的标签
  String _getSortLabel(String sortBy) {
    switch (sortBy) {
      case 'name':
        return '按名称';
      case 'date':
        return '按时间';
      case 'size':
        return '按大小';
      default:
        return '按名称';
    }
  }

  /// 获取排序类型的图标
  IconData _getSortIcon(String sortBy) {
    switch (sortBy) {
      case 'name':
        return Icons.sort_by_alpha;
      case 'date':
        return Icons.schedule;
      case 'size':
        return Icons.data_usage;
      default:
        return Icons.sort_by_alpha;
    }
  }

  /// 获取排序类型的颜色
  Color _getSortColor(String sortBy) {
    switch (sortBy) {
      case 'name':
        return Colors.blue;
      case 'date':
        return Colors.orange;
      case 'size':
        return Colors.green;
      default:
        return theme.primary;
    }
  }
}
