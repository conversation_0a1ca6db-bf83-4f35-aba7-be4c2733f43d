import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 切换按钮组组件
class ToggleButtonGroup extends StatelessWidget {
  final AppTheme theme;
  final List<ToggleButtonItem> items;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const ToggleButtonGroup({
    super.key,
    required this.theme,
    required this.items,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.fromLTRB(0, 8, 0, 16),
      padding: padding ?? const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: theme.onSurface.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.onSurface.withValues(alpha: 0.08),
        ),
      ),
      child: Row(
        children: items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          return Expanded(
            child: Row(
              children: [
                if (index > 0) const SizedBox(width: 10),
                Expanded(
                  child: _buildToggleButton(item),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建切换按钮
  Widget _buildToggleButton(ToggleButtonItem item) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            if (!item.isSelected) {
              HapticFeedback.lightImpact();
              item.onTap();
            }
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 14),
            decoration: BoxDecoration(
              color: item.isSelected ? theme.primary : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              boxShadow: item.isSelected
                  ? [
                      BoxShadow(
                        color: theme.primary.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  item.icon,
                  size: 16,
                  color: item.isSelected ? theme.onPrimary : theme.onSurface.withValues(alpha: 0.6),
                ),
                const SizedBox(width: 6),
                Text(
                  item.label,
                  style: theme.getTextStyle(
                    fontSize: 14,
                    fontWeight: item.isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: item.isSelected ? theme.onPrimary : theme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 切换按钮项数据类
class ToggleButtonItem {
  final String label;
  final IconData icon;
  final bool isSelected;
  final VoidCallback onTap;

  const ToggleButtonItem({
    required this.label,
    required this.icon,
    required this.isSelected,
    required this.onTap,
  });
}
