import 'package:flutter/material.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';
import 'package:nicexfer_app/presentation/global_widgets/sheet_base/bottom_sheet_option_item.dart';

/// 带选中状态的选项项组件
class SelectableBottomSheetOptionItem extends StatelessWidget {
  final AppTheme theme;
  final IconData icon;
  final String title;
  final String? description;
  final VoidCallback onTap;
  final bool isSelected;
  final double iconSize;
  final EdgeInsets? padding;

  const SelectableBottomSheetOptionItem({
    super.key,
    required this.theme,
    required this.icon,
    required this.title,
    this.description,
    required this.onTap,
    required this.isSelected,
    this.iconSize = 22,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return BottomSheetOptionItem(
      theme: theme,
      icon: icon,
      title: title,
      description: description,
      onTap: onTap,
      isSelected: isSelected,
      showChevron: false,
      iconSize: iconSize,
      padding: padding,
      trailing: AnimatedScale(
        scale: isSelected ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        child: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: theme.primary,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            Icons.check,
            size: 16,
            color: theme.onPrimary,
          ),
        ),
      ),
    );
  }
}
