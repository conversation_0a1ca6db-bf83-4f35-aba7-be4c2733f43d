import 'package:flutter/material.dart';

import 'package:lucide_icons_flutter/lucide_icons.dart';

import 'package:nicexfer_app/core/theme/theme_helper.dart';

import 'package:nicexfer_app/presentation/modules/files/controllers/files_controller.dart';
import 'package:nicexfer_app/presentation/global_widgets/dialogs/confirm_dialog_header.dart';
import 'package:nicexfer_app/presentation/global_widgets/dialogs/danger_warning.dart';
import 'package:nicexfer_app/presentation/modules/files/widgets/file_display/file_info_card.dart';
import 'package:nicexfer_app/presentation/global_widgets/sheet_base/bottom_sheet_header.dart';
import '../../../../global_widgets/sheet_base/base_bottom_sheet.dart';
import '../../../../global_widgets/sheet_base/bottom_sheet_option_item.dart';
import '../../../../global_widgets/dialogs/base_confirm_dialog.dart';

/// 文件选项底部弹窗组件
class FileOptionsBottomSheet extends StatelessWidget {
  final FileItem file;
  final AppTheme theme;
  final VoidCallback onView;
  final VoidCallback onShare;
  final VoidCallback onDownload;
  final VoidCallback onCopy;
  final VoidCallback onDelete;

  const FileOptionsBottomSheet({
    super.key,
    required this.file,
    required this.theme,
    required this.onView,
    required this.onShare,
    required this.onDownload,
    required this.onCopy,
    required this.onDelete,
  });

  /// 显示文件选项底部弹窗
  static void show(
    BuildContext context,
    FileItem file,
    AppTheme theme, {
    required VoidCallback onView,
    required VoidCallback onShare,
    required VoidCallback onDownload,
    required VoidCallback onCopy,
    required VoidCallback onDelete,
  }) {
    BaseBottomSheet.show(
      context,
      FileOptionsBottomSheet(
        file: file,
        theme: theme,
        onView: onView,
        onShare: onShare,
        onDownload: onDownload,
        onCopy: onCopy,
        onDelete: onDelete,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final fileExtension = _getFileExtension(file.name);
    final fileTypeText = fileExtension.isNotEmpty ? fileExtension.toUpperCase() : '文件';

    return BaseBottomSheet(
      theme: theme,
      header: BottomSheetHeader(
        theme: theme,
        icon: _getFileIcon(file),
        title: file.name,
        subtitle: '$fileTypeText • ${_getFileSize(file.size ?? 0)}',
        actionHint: '选择要执行的操作',
        iconColor: _getFileIconColor(file),
        backgroundColor: _getFileIconColor(file),
      ),
      children: [
        // 操作选项
        Column(
          children: [
            BottomSheetOptionItem(
              theme: theme,
              icon: LucideIcons.eye,
              title: '查看',
              description: '打开并查看文件内容',
              onTap: () {
                Navigator.pop(context);
                onView();
              },
            ),
            const SizedBox(height: 4),
            BottomSheetOptionItem(
              theme: theme,
              icon: LucideIcons.share,
              title: '分享',
              description: '分享文件给其他人',
              onTap: () {
                Navigator.pop(context);
                onShare();
              },
            ),
            const SizedBox(height: 4),
            BottomSheetOptionItem(
              theme: theme,
              icon: LucideIcons.download,
              title: '下载',
              description: '下载文件到本地',
              onTap: () {
                Navigator.pop(context);
                onDownload();
              },
            ),
            const SizedBox(height: 4),
            BottomSheetOptionItem(
              theme: theme,
              icon: LucideIcons.copy,
              title: '复制',
              description: '复制文件到剪贴板',
              onTap: () {
                Navigator.pop(context);
                onCopy();
              },
            ),
            const SizedBox(height: 4),
            BottomSheetOptionItem(
              theme: theme,
              icon: LucideIcons.trash2,
              title: '删除',
              description: '永久删除此文件',
              isDestructive: true,
              onTap: () {
                Navigator.pop(context);
                onDelete();
              },
            ),
          ],
        ),
      ],
    );
  }

  /// 获取文件图标颜色
  Color _getFileIconColor(FileItem file) {
    if (file.isFolder) {
      if (file.name.contains('隐藏')) return Colors.grey;
      if (file.name.contains('相册')) return Colors.purple;
      if (file.name.contains('应用')) return Colors.blue;
      if (file.name.contains('备份')) return Colors.orange;
      return theme.primary;
    }

    final extension = _getFileExtension(file.name).toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].contains(extension)) {
      return Colors.green;
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].contains(extension)) {
      return Colors.red;
    } else if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'].contains(extension)) {
      return Colors.purple;
    } else if (['pdf'].contains(extension)) {
      return Colors.red;
    } else if (['doc', 'docx', 'txt', 'rtf', 'odt'].contains(extension)) {
      return Colors.blue;
    } else if (['xls', 'xlsx', 'csv', 'ods'].contains(extension)) {
      return Colors.green;
    } else if (['ppt', 'pptx', 'odp'].contains(extension)) {
      return Colors.orange;
    } else if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].contains(extension)) {
      return Colors.brown;
    }
    return theme.primary;
  }

  /// 获取文件图标
  IconData _getFileIcon(FileItem file) {
    if (file.isFolder) {
      if (file.name.contains('隐藏')) return LucideIcons.eyeOff;
      if (file.name.contains('相册')) return LucideIcons.images;
      if (file.name.contains('应用')) return LucideIcons.smartphone;
      if (file.name.contains('备份')) return LucideIcons.hardDrive;
      return LucideIcons.folder;
    }

    // 根据文件扩展名返回对应图标
    final extension = _getFileExtension(file.name).toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].contains(extension)) {
      return LucideIcons.image;
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].contains(extension)) {
      return LucideIcons.video;
    } else if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'].contains(extension)) {
      return LucideIcons.music;
    } else if (['pdf'].contains(extension)) {
      return LucideIcons.fileText;
    } else if (['doc', 'docx', 'txt', 'rtf', 'odt'].contains(extension)) {
      return LucideIcons.fileText;
    } else if (['xls', 'xlsx', 'csv', 'ods'].contains(extension)) {
      return LucideIcons.sheet;
    } else if (['ppt', 'pptx', 'odp'].contains(extension)) {
      return LucideIcons.presentation;
    } else if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].contains(extension)) {
      return LucideIcons.archive;
    }
    return LucideIcons.file;
  }

  /// 获取文件扩展名
  String _getFileExtension(String fileName) {
    final parts = fileName.split('.');
    return parts.length > 1 ? parts.last : '';
  }

  /// 格式化文件大小
  String _getFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  /// 显示删除确认对话框
  static void showDeleteConfirmation(
    BuildContext context,
    FileItem file,
    AppTheme theme,
    VoidCallback onConfirm,
  ) {
    BaseConfirmDialog.show(
      context: context,
      theme: theme,
      header: ConfirmDialogHeader(
        icon: Icons.delete_forever_rounded,
        title: '永久删除',
        subtitle: '此操作不可撤销，请谨慎操作',
        iconColor: Colors.red,
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        theme: theme,
      ),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            '您即将删除以下${file.isFolder ? '文件夹' : '文件'}：',
            style: theme.getTextStyle(
              fontSize: 16,
              color: theme.onSurface.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 16),
          FileInfoCard(
            file: file,
            theme: theme,
          ),
          const SizedBox(height: 16),
          DangerWarning(
            message: '删除后无法恢复，请确认您的操作',
            theme: theme,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: theme.surface,
            side: BorderSide(
              color: theme.onSurface.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Text(
            '取消',
            style: theme.getTextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: theme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
            onConfirm();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            elevation: 0,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            shadowColor: Colors.red.withValues(alpha: 0.3),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.delete_forever_rounded,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                '确认删除',
                style: theme.getTextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
