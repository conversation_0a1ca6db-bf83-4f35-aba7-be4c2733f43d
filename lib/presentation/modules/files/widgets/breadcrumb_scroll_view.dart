import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';

import 'package:nicexfer_app/core/theme/theme_helper.dart';

import '../controllers/files_controller.dart';

/// 面包屑滚动视图组件
///
/// 提供水平滚动的面包屑导航，支持：
/// - 自动滚动到当前位置
/// - 无障碍访问
/// - 键盘导航
/// - 悬停效果
/// - 响应式设计
class BreadcrumbScrollView extends StatefulWidget {
  /// 文件控制器
  final FilesController controller;

  /// 应用主题
  final AppTheme theme;

  /// 面包屑项点击回调
  final Function(int) onBreadcrumbTap;

  /// 根目录点击回调
  final VoidCallback onRootTap;

  const BreadcrumbScrollView({
    super.key,
    required this.controller,
    required this.theme,
    required this.onBreadcrumbTap,
    required this.onRootTap,
  });

  @override
  State<BreadcrumbScrollView> createState() => _BreadcrumbScrollViewState();
}

/// 面包屑组件的样式常量
class _BreadcrumbConstants {
  static const double itemPaddingHorizontal = 12.0;
  static const double itemPaddingVertical = 8.0;
  static const double itemBorderRadius = 8.0;
  static const double separatorMargin = 6.0;
  static const double separatorIconSize = 14.0;
  static const double textFontSize = 14.0;
  static const double minTouchTargetSize = 44.0;
  static const Duration scrollAnimationDuration = Duration(milliseconds: 300);
  static const Duration hoverAnimationDuration = Duration(milliseconds: 200);
  static const Curve scrollAnimationCurve = Curves.easeOutCubic;

  /// 根目录图标
  static const IconData rootIcon = LucideIcons.house;
  static const double rootIconSize = 16.0;
}

class _BreadcrumbScrollViewState extends State<BreadcrumbScrollView> with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late Worker _pathWorker;

  @override
  void initState() {
    super.initState();
    _initializeScrollController();
    _setupPathListener();
  }

  @override
  void dispose() {
    _pathWorker.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 初始化滚动控制器
  void _initializeScrollController() {
    _scrollController = ScrollController();
  }

  /// 设置路径变化监听器
  void _setupPathListener() {
    _pathWorker = ever(widget.controller.currentPath, (_) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToEnd();
      });
    });
  }

  /// 滚动到最右侧
  ///
  /// 当路径发生变化时，自动滚动到最新的面包屑项
  void _scrollToEnd() {
    if (!mounted || !_scrollController.hasClients) return;

    try {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: _BreadcrumbConstants.scrollAnimationDuration,
        curve: _BreadcrumbConstants.scrollAnimationCurve,
      );
    } catch (e) {
      // 静默处理滚动异常，避免影响用户体验
      debugPrint('面包屑滚动异常: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: '文件路径导航',
      child: Obx(() {
        final pathItems = widget.controller.currentPath;

        return SingleChildScrollView(
          controller: _scrollController,
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          child: IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 根目录
                _buildBreadcrumbItem(
                  context: context,
                  icon: _BreadcrumbConstants.rootIcon,
                  isLast: pathItems.isEmpty,
                  onTap: widget.onRootTap,
                  semanticLabel: '返回根目录',
                ),

                // 路径分隔符和路径项
                ...pathItems.asMap().entries.map((entry) {
                  final index = entry.key;
                  final pathName = entry.value;
                  final isLast = index == pathItems.length - 1;

                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildBreadcrumbSeparator(),
                      _buildBreadcrumbItem(
                        context: context,
                        text: pathName,
                        isLast: isLast,
                        onTap: isLast ? null : () => widget.onBreadcrumbTap(index),
                        semanticLabel: isLast ? '当前目录: $pathName' : '导航到 $pathName',
                      ),
                    ],
                  );
                }),
              ],
            ),
          ),
        );
      }),
    );
  }

  /// 构建面包屑分隔符
  Widget _buildBreadcrumbSeparator() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: _BreadcrumbConstants.separatorMargin,
      ),
      child: Icon(
        LucideIcons.chevronRight,
        size: _BreadcrumbConstants.separatorIconSize,
        color: widget.theme.onSurface.withValues(alpha: 0.4),
      ),
    );
  }

  /// 构建面包屑项
  ///
  /// [context] - 构建上下文
  /// [text] - 显示文本（当icon为null时使用）
  /// [icon] - 显示图标（优先于text显示）
  /// [isLast] - 是否为最后一项（当前位置）
  /// [onTap] - 点击回调，为null时不可点击
  /// [semanticLabel] - 无障碍语义标签
  Widget _buildBreadcrumbItem({
    required BuildContext context,
    String? text,
    IconData? icon,
    required bool isLast,
    required VoidCallback? onTap,
    required String semanticLabel,
  }) {
    assert(text != null || icon != null, '文本和图标至少需要提供一个');
    final theme = widget.theme;
    final isClickable = onTap != null;

    return Semantics(
      label: semanticLabel,
      button: isClickable,
      child: _BreadcrumbItemWidget(
        text: text,
        icon: icon,
        isLast: isLast,
        isClickable: isClickable,
        theme: theme,
        onTap: onTap,
      ),
    );
  }
}

/// 面包屑项组件
///
/// 独立的StatefulWidget以支持悬停状态和动画
class _BreadcrumbItemWidget extends StatefulWidget {
  final String? text;
  final IconData? icon;
  final bool isLast;
  final bool isClickable;
  final AppTheme theme;
  final VoidCallback? onTap;

  const _BreadcrumbItemWidget({
    this.text,
    this.icon,
    required this.isLast,
    required this.isClickable,
    required this.theme,
    required this.onTap,
  });

  @override
  State<_BreadcrumbItemWidget> createState() => _BreadcrumbItemWidgetState();
}

class _BreadcrumbItemWidgetState extends State<_BreadcrumbItemWidget> with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: _BreadcrumbConstants.hoverAnimationDuration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleHoverChange(bool isHovered) {
    if (!widget.isClickable) return;

    setState(() {
      _isHovered = isHovered;
    });

    if (isHovered) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _handleTap() {
    if (widget.onTap != null) {
      // 触觉反馈
      HapticFeedback.lightImpact();
      widget.onTap!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: widget.isClickable ? _handleTap : null,
              onHover: _handleHoverChange,
              borderRadius: BorderRadius.circular(_BreadcrumbConstants.itemBorderRadius),
              splashColor: widget.theme.primary.withValues(alpha: 0.1),
              highlightColor: widget.theme.primary.withValues(alpha: 0.05),
              child: AnimatedContainer(
                duration: _BreadcrumbConstants.hoverAnimationDuration,
                curve: Curves.easeInOut,
                constraints: const BoxConstraints(
                  minHeight: _BreadcrumbConstants.minTouchTargetSize,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: _BreadcrumbConstants.itemPaddingHorizontal,
                  vertical: _BreadcrumbConstants.itemPaddingVertical,
                ),
                decoration: BoxDecoration(
                  color: _getBackgroundColor(),
                  borderRadius: BorderRadius.circular(_BreadcrumbConstants.itemBorderRadius),
                  border: widget.isLast
                      ? Border.all(
                          color: widget.theme.primary.withValues(alpha: 0.3),
                          width: 1,
                        )
                      : _isHovered && widget.isClickable
                          ? Border.all(
                              color: widget.theme.primary.withValues(alpha: 0.2),
                              width: 1,
                            )
                          : null,
                  boxShadow: widget.isLast
                      ? [
                          BoxShadow(
                            color: widget.theme.primary.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Center(
                  child: widget.icon != null
                      ? Icon(
                          widget.icon!,
                          size: _BreadcrumbConstants.rootIconSize,
                          color: _getTextColor(),
                        )
                      : Text(
                          widget.text!,
                          style: TextStyle(
                            color: _getTextColor(),
                            fontSize: _BreadcrumbConstants.textFontSize,
                            fontWeight: widget.isLast ? FontWeight.w600 : FontWeight.w500,
                            height: 1.2,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getBackgroundColor() {
    if (widget.isLast) {
      return widget.theme.primary.withValues(alpha: 0.15);
    }
    if (_isHovered && widget.isClickable) {
      return widget.theme.primary.withValues(alpha: 0.08);
    }
    return Colors.transparent;
  }

  Color _getTextColor() {
    if (widget.isLast) {
      return widget.theme.primary;
    }
    if (_isHovered && widget.isClickable) {
      return widget.theme.primary.withValues(alpha: 0.8);
    }
    return widget.theme.onSurface.withValues(alpha: 0.7);
  }
}
