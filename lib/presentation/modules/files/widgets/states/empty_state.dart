import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 简洁的空状态组件
class EmptyState extends StatelessWidget {
  /// 应用主题
  final AppTheme theme;

  /// 空状态图标
  final IconData icon;

  /// 主标题
  final String title;

  /// 副标题
  final String? subtitle;

  /// 图标大小
  final double iconSize;

  const EmptyState({
    super.key,
    required this.theme,
    this.icon = LucideIcons.folderOpen,
    this.title = '暂无文件',
    this.subtitle,
    this.iconSize = 48,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 简洁的图标
            Icon(
              icon,
              size: iconSize,
              color: theme.onSurface.withValues(alpha: 0.4),
            ),
            const SizedBox(height: 16),
            // 简洁的标题
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: theme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            // 简洁的副标题
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle!,
                style: TextStyle(
                  fontSize: 14,
                  color: theme.onSurface.withValues(alpha: 0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
