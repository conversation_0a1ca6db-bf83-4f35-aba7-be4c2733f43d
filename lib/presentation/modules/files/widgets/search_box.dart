import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';

import 'package:nicexfer_app/core/theme/theme_helper.dart';
import '../controllers/files_controller.dart';

/// 优化的搜索框组件
/// 支持防抖搜索、搜索状态指示、搜索历史等功能
class SearchBox extends StatefulWidget {
  const SearchBox({super.key});

  @override
  State<SearchBox> createState() => _SearchBoxState();
}

class _SearchBoxState extends State<SearchBox> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  Timer? _debounceTimer;

  FilesController get controller => Get.find<FilesController>();

  @override
  void initState() {
    super.initState();
    // 监听控制器的搜索关键词变化，同步到文本框
    ever(controller.searchKeyword, (String keyword) {
      if (_textController.text != keyword) {
        _textController.text = keyword;
      }
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 防抖搜索
  void _onSearchChanged(String value) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      controller.searchFiles(value);
    });
  }

  /// 清空搜索
  void _clearSearch() {
    _textController.clear();
    controller.searchFiles('');
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Container(
      height: 52,
      decoration: BoxDecoration(
        color: theme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.outline.withValues(alpha: 0.12),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadow.withValues(alpha: 0.04),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: -2,
          ),
          BoxShadow(
            color: theme.shadow.withValues(alpha: 0.02),
            blurRadius: 6,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: TextField(
        controller: _textController,
        focusNode: _focusNode,
        onChanged: _onSearchChanged,
        decoration: InputDecoration(
          hintText: '搜索文件和文件夹...',
          hintStyle: TextStyle(
            color: theme.onSurface.withValues(alpha: 0.45),
            fontSize: 16,
            fontWeight: FontWeight.w400,
          ),
          prefixIcon: Container(
            padding: const EdgeInsets.all(14),
            child: Icon(
              LucideIcons.search,
              size: 20,
              color: theme.onSurface.withValues(alpha: 0.5),
            ),
          ),
          suffixIcon: _buildSuffixIcon(theme),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        ),
        style: TextStyle(
          color: theme.onSurface,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建后缀图标（搜索状态指示器或清除按钮）
  Widget _buildSuffixIcon(AppTheme theme) {
    return Obx(() {
      // 显示搜索中状态
      if (controller.isSearching.value) {
        return Container(
          margin: const EdgeInsets.only(right: 12),
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.primary.withValues(alpha: 0.6),
            ),
          ),
        );
      }

      // 显示清除按钮
      if (controller.searchKeyword.value.isNotEmpty) {
        return Container(
          margin: const EdgeInsets.only(right: 8),
          child: IconButton(
            icon: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: theme.onSurface.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                LucideIcons.x,
                size: 16,
                color: theme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            onPressed: _clearSearch,
            splashRadius: 20,
          ),
        );
      }

      return const SizedBox.shrink();
    });
  }
}
