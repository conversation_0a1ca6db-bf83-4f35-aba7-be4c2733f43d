import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:glass/glass.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:nicexfer_app/presentation/global_widgets/buttons/unified_header_action_button.dart';
import 'package:nicexfer_app/presentation/global_widgets/chips/unified_filter_chip.dart';
import '../controllers/files_controller.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';
import '../widgets/breadcrumb_scroll_view.dart';
import '../widgets/search_box.dart';
import '../widgets/file_sheets/sort_bottom_sheet.dart';
import '../widgets/file_sheets/file_options_bottom_sheet.dart';
import '../widgets/file_sheets/more_options_bottom_sheet.dart';
import '../../../global_widgets/buttons/quick_action_button.dart';
import '../../../global_widgets/buttons/header_action_button.dart';
import '../widgets/states/loading_state.dart';
import '../widgets/states/empty_state.dart';
import '../widgets/file_display/file_icon.dart';
import '../widgets/file_display/file_source_label.dart';
import '../widgets/file_display/file_size_display.dart';
import '../widgets/file_display/file_type_label.dart';

class FilesPage extends GetView<FilesController> {
  const FilesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Material(
      color: Colors.transparent,
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            elevation: 0,
            centerTitle: false,
            floating: true,
            snap: true,
            pinned: true,
            expandedHeight: 225,
            backgroundColor: theme.surface.withValues(alpha: 0.1),
            surfaceTintColor: Colors.transparent,
            flexibleSpace: FlexibleSpaceBar(
              background: _buildHeaderBackground(context, theme),
              titlePadding: EdgeInsets.zero,
            ).asGlass(
              enabled: true,
              tintColor: Colors.transparent,
              blurX: 5,
              blurY: 5,
              frosted: false,
            ),
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(107),
              child: Obx(
                () => Column(
                  children: [
                    controller.isInFolder.value
                        ? _buildBreadcrumbNavigation(context, theme)
                        : _buildCategoryTabs(context, theme),
                    _buildSortAndViewOptions(context, theme),
                  ],
                ),
              ),
            ),
          ),
        ],
        body: _buildFileList(context, theme),
      ),
    );
  }

  /// 构建头部背景
  Widget _buildHeaderBackground(BuildContext context, AppTheme theme) {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部标题和操作区域
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Obx(
                        () => AnimatedSwitcher(
                          duration: Duration(milliseconds: 300),
                          child: Text(
                            controller.isInFolder.value ? controller.currentFolderName.value : '我的文件',
                            key: ValueKey(controller.isInFolder.value ? controller.currentFolderName.value : '我的文件'),
                            style: theme.getTextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w800,
                              color: theme.onSurface,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 4),
                      Obx(
                        () => AnimatedSwitcher(
                          duration: Duration(milliseconds: 300),
                          child: Text(
                            controller.isInFolder.value
                                ? '${controller.fileList.length} 个文件'
                                : '${controller.fileList.length} 个项目',
                            key: ValueKey('${controller.isInFolder.value}-${controller.fileList.length}'),
                            style: theme.getTextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: theme.onSurface.withValues(alpha: 0.65),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    UnifiedHeaderActionButton.modern(
                      icon: LucideIcons.arrowUpDown,
                      onPressed: () => Get.toNamed('/notfound'),
                    ),
                    SizedBox(width: 10),
                    UnifiedHeaderActionButton.modern(
                      icon: LucideIcons.ellipsisVertical,
                      isPrimary: true,
                      onPressed: () => _showMoreOptions(context),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 8),
            // 搜索区域
            _buildSearchSection(context, theme),
          ],
        ),
      ),
    );
  }

  /// 构建搜索区域
  Widget _buildSearchSection(BuildContext context, AppTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SearchBox(),
      ],
    );
  }

  /// 构建分类标签页
  Widget _buildCategoryTabs(BuildContext context, AppTheme theme) {
    return Container(
      height: 55,
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: FileType.values.length,
        itemBuilder: (context, index) {
          final fileType = FileType.values[index];

          return Padding(
            padding: EdgeInsets.only(right: 8),
            child: Obx(() {
              final isSelected = controller.selectedFileType.value == fileType;
              return UnifiedFilterChip.modern(
                label: fileType.label,
                isSelected: isSelected,
                onTap: () => controller.changeFileType(fileType),
              );
            }),
          );
        },
      ),
    );
  }

  /// 构建文件列表
  Widget _buildFileList(BuildContext context, AppTheme theme) {
    return Obx(() {
      if (controller.isLoading.value) {
        return LoadingState(
          theme: theme,
          message: '正在加载文件...',
        );
      }

      if (controller.fileList.isEmpty) {
        return EmptyState(
          theme: theme,
          icon: LucideIcons.folderOpen,
          title: '还没有文件',
          subtitle: '开始上传您的第一个文件\n或创建新的文件夹',
        );
      }

      // 根据视图模式显示不同的布局
      if (controller.viewMode.value == 'grid') {
        return OrientationBuilder(
          builder: (context, orientation) {
            return GridView.builder(
              padding: EdgeInsets.fromLTRB(8, 8, 8, 8),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: orientation == Orientation.landscape ? 3 : 2,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 1.2,
              ),
              itemCount: controller.fileList.length,
              itemBuilder: (context, index) {
                final file = controller.fileList[index];
                return _buildFileGridItem(context, theme, file, index);
              },
            );
          },
        );
      } else {
        return ListView.builder(
          padding: EdgeInsets.fromLTRB(8, 8, 8, 8),
          itemCount: controller.fileList.length,
          itemBuilder: (context, index) {
            final file = controller.fileList[index];
            return _buildFileItem(context, theme, file, index);
          },
        );
      }
    });
  }

  /// 构建文件项
  Widget _buildFileItem(BuildContext context, AppTheme theme, FileItem file, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openFile(file),
          borderRadius: BorderRadius.circular(12),
          child: AnimatedContainer(
            duration: Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: theme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.outline.withValues(alpha: 0.08),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.shadow.withValues(alpha: 0.04),
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // 主要内容区域
                Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // 文件图标/缩略图
                      Hero(
                        tag: 'file_icon_${file.id}',
                        child: FileIcon(
                          fileName: file.name,
                          isFolder: file.isFolder,
                          theme: theme,
                          iconSize: 24,
                          borderRadius: 12,
                        ),
                      ),
                      SizedBox(width: 12),
                      // 文件信息
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 文件名和类型标签
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    file.name,
                                    style: theme.getTextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w600,
                                      color: theme.onSurface,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                FileTypeLabel(
                                  fileType: file.isFolder ? '' : file.name.split('.').last,
                                  theme: theme,
                                  isFolder: file.isFolder,
                                  fontSize: 10,
                                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                  borderRadius: 4,
                                ),
                              ],
                            ),
                            SizedBox(height: 4),
                            // 文件详细信息
                            Row(
                              children: [
                                // 来源标签
                                if (file.source.isNotEmpty) ...[
                                  FileSourceLabel(
                                    source: file.source,
                                    theme: theme,
                                    fontSize: 11,
                                    padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                    borderRadius: 4,
                                  ),
                                  SizedBox(width: 8),
                                ],
                                // 文件大小
                                if (file.size != null && file.size! > 0) ...[
                                  FileSizeDisplay(
                                    size: file.size!,
                                    theme: theme,
                                    fontSize: 12,
                                    iconSize: 12,
                                    spacing: 4,
                                  ),
                                  SizedBox(width: 12),
                                ],
                                // 修改时间
                                Icon(
                                  LucideIcons.clock,
                                  size: 12,
                                  color: theme.onSurface.withValues(alpha: 0.5),
                                ),
                                SizedBox(width: 4),
                                Text(
                                  _formatDate(file.date),
                                  style: theme.getTextStyle(
                                    fontSize: 12,
                                    color: theme.onSurface.withValues(alpha: 0.6),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      // 更多操作按钮
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () => _showFileOptions(context, file),
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            padding: EdgeInsets.all(6),
                            child: Icon(
                              LucideIcons.ellipsisVertical,
                              color: theme.onSurface.withValues(alpha: 0.4),
                              size: 18,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // 底部操作栏（可选）
                if (_shouldShowQuickActions(file))
                  Container(
                    padding: EdgeInsets.fromLTRB(16, 0, 16, 12),
                    child: Row(
                      children: [
                        QuickActionButton(
                          icon: LucideIcons.share2,
                          onTap: () => _shareFile(file),
                          theme: theme,
                        ),
                        SizedBox(width: 12),
                        QuickActionButton(
                          icon: LucideIcons.download,
                          onTap: () => _downloadFile(file),
                          theme: theme,
                        ),
                        Spacer(),
                        Text(
                          '${index + 1}/${controller.fileList.length}',
                          style: theme.getTextStyle(
                            fontSize: 11,
                            color: theme.onSurface.withValues(alpha: 0.4),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 判断是否显示快捷操作
  bool _shouldShowQuickActions(FileItem file) {
    // 可以根据文件类型或其他条件决定是否显示快捷操作
    return !file.isFolder;
  }

  /// 分享文件
  void _shareFile(FileItem file) {
    // 实现分享功能
    Get.snackbar('分享', '分享文件: ${file.name}');
  }

  /// 下载文件
  void _downloadFile(FileItem file) {
    // 实现下载功能
    Get.snackbar('下载', '下载文件: ${file.name}');
  }

  /// 获取文件图标
  IconData _getFileIcon(FileItem file) {
    if (file.isFolder) {
      if (file.name.contains('隐藏')) return LucideIcons.eyeOff;
      if (file.name.contains('相册')) return LucideIcons.images;
      if (file.name.contains('应用')) return LucideIcons.smartphone;
      if (file.name.contains('备份')) return LucideIcons.hardDrive;
      return LucideIcons.folder;
    }

    // 根据文件扩展名返回对应图标
    final extension = _getFileExtension(file.name).toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].contains(extension)) {
      return LucideIcons.image;
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].contains(extension)) {
      return LucideIcons.video;
    } else if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'].contains(extension)) {
      return LucideIcons.music;
    } else if (['pdf'].contains(extension)) {
      return LucideIcons.fileText;
    } else if (['doc', 'docx', 'txt', 'rtf', 'odt'].contains(extension)) {
      return LucideIcons.fileText;
    } else if (['xls', 'xlsx', 'csv', 'ods'].contains(extension)) {
      return LucideIcons.sheet;
    } else if (['ppt', 'pptx', 'odp'].contains(extension)) {
      return LucideIcons.presentation;
    } else if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].contains(extension)) {
      return LucideIcons.archive;
    }
    return LucideIcons.file;
  }

  /// 获取文件扩展名
  String _getFileExtension(String fileName) {
    final parts = fileName.split('.');
    return parts.length > 1 ? parts.last : '';
  }

  /// 判断文件是否可预览
  bool _isPreviewableFile(FileItem file) {
    if (file.isFolder) return false;

    final extension = _getFileExtension(file.name).toLowerCase();
    // 图片文件可预览
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
      return true;
    }
    // 视频文件可预览（显示缩略图）
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].contains(extension)) {
      return true;
    }
    // PDF文件可预览
    if (['pdf'].contains(extension)) {
      return true;
    }
    return false;
  }

  /// 获取文件图标颜色
  List<Color> _getFileIconColors(AppTheme theme, FileItem file) {
    if (file.name.contains('隐藏')) {
      return [Color(0xFF6366F1), Color(0xFF8B5CF6)];
    } else if (file.name.contains('相册')) {
      return [Color(0xFFEC4899), Color(0xFFF97316)];
    } else if (file.name.contains('应用')) {
      return [Color(0xFF10B981), Color(0xFF06B6D4)];
    } else if (file.name.contains('备份')) {
      return [Color(0xFF8B5CF6), Color(0xFF3B82F6)];
    } else if (file.name.contains('iPhone')) {
      return [Color(0xFF1F2937), Color(0xFF4B5563)];
    }
    return [theme.primary, theme.secondary];
  }

  /// 打开文件
  void _openFile(FileItem file) {
    if (file.isFolder) {
      // 如果是文件夹，进入文件夹
      controller.enterFolder(file);
    } else {
      // TODO: 实现文件打开功能
      Get.snackbar(
        '打开文件',
        '正在打开 ${file.name}',
        snackPosition: SnackPosition.BOTTOM,
        margin: EdgeInsets.all(16),
      );
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    }
  }

  /// 显示更多选项
  void _showMoreOptions(BuildContext context) {
    final theme = AppTheme.of(context);
    MoreOptionsBottomSheet.show(
      context,
      theme,
      onUpload: () {
        // TODO: 实现上传文件功能
      },
      onCreateFolder: () {
        // TODO: 实现新建文件夹功能
      },
      onSettings: () {
        // TODO: 实现设置功能
      },
    );
  }

  /// 显示文件选项
  void _showFileOptions(BuildContext context, FileItem file) {
    final theme = AppTheme.of(context);
    FileOptionsBottomSheet.show(
      context,
      file,
      theme,
      onView: () => _openFile(file),
      onShare: () {
        // TODO: 实现分享功能
      },
      onDownload: () {
        // TODO: 实现下载功能
      },
      onCopy: () {
        // TODO: 实现复制功能
      },
      onDelete: () => FileOptionsBottomSheet.showDeleteConfirmation(
        context,
        file,
        theme,
        () {
          // TODO: 实现删除功能
          Get.snackbar(
            '删除成功',
            '已删除 ${file.name}',
            snackPosition: SnackPosition.BOTTOM,
            margin: const EdgeInsets.all(16),
            backgroundColor: theme.primary.withValues(alpha: 0.1),
            colorText: theme.onSurface,
            borderRadius: 12,
          );
        },
      ),
    );
  }

  /// 构建排序和视图选项
  Widget _buildSortAndViewOptions(BuildContext context, AppTheme theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Row(
        children: [
          // 排序选项 - 改为可点击的按钮
          Expanded(
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _showSortBottomSheet(context, theme),
                borderRadius: BorderRadius.circular(12),
                splashColor: theme.primary.withValues(alpha: 0.1),
                highlightColor: theme.primary.withValues(alpha: 0.05),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Obx(() => Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _getSortIcon(controller.sortBy.value),
                                size: 16,
                                color: theme.primary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _getSortLabel(controller.sortBy.value),
                                style: theme.getTextStyle(
                                  fontSize: 14,
                                  color: theme.primary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          )),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.keyboard_arrow_down,
                        size: 16,
                        color: theme.onSurface.withValues(alpha: 0.6),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // 视图切换
          Obx(() => _buildViewSegmentedButton(theme)),
        ],
      ),
    );
  }

  /// 获取排序标签
  String _getSortLabel(String sortType) {
    switch (sortType) {
      case 'name':
        return '名称';
      case 'date':
        return '日期';
      case 'size':
        return '大小';
      default:
        return '名称';
    }
  }

  /// 获取排序图标
  IconData _getSortIcon(String sortType) {
    switch (sortType) {
      case 'name':
        return Icons.sort_by_alpha;
      case 'date':
        return Icons.access_time;
      case 'size':
        return Icons.storage;
      default:
        return Icons.sort_by_alpha;
    }
  }

  /// 显示排序选项底部弹窗
  void _showSortBottomSheet(BuildContext context, AppTheme theme) {
    SortBottomSheet.show(context, controller, theme);
  }

  /// 构建视图切换分段按钮
  Widget _buildViewSegmentedButton(AppTheme theme) {
    final viewModes = [
      {'mode': 'list', 'icon': Icons.view_list, 'label': '列表'},
      {'mode': 'grid', 'icon': Icons.grid_view, 'label': '网格'},
    ];

    return Container(
      decoration: BoxDecoration(
        color: theme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.outline.withValues(alpha: 0.12),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadow.withValues(alpha: 0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: viewModes.map((viewMode) {
          final mode = viewMode['mode'] as String;
          final icon = viewMode['icon'] as IconData;
          final isSelected = controller.viewMode.value == mode;
          final isFirst = viewModes.first['mode'] == mode;
          final isLast = viewModes.last['mode'] == mode;

          return Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => controller.changeViewMode(mode),
              borderRadius: BorderRadius.horizontal(
                left: isFirst ? const Radius.circular(10) : Radius.zero,
                right: isLast ? const Radius.circular(10) : Radius.zero,
              ),
              splashColor: theme.primary.withValues(alpha: 0.1),
              highlightColor: theme.primary.withValues(alpha: 0.05),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOutCubic,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? theme.primary.withValues(alpha: 0.12) : Colors.transparent,
                  borderRadius: BorderRadius.horizontal(
                    left: isFirst ? const Radius.circular(11) : Radius.zero,
                    right: isLast ? const Radius.circular(11) : Radius.zero,
                  ),
                  border: !isFirst
                      ? Border(
                          left: BorderSide(
                            color: theme.outline.withValues(alpha: 0.08),
                            width: 1,
                          ),
                        )
                      : null,
                ),
                child: AnimatedScale(
                  duration: const Duration(milliseconds: 200),
                  scale: isSelected ? 1.05 : 1.0,
                  child: Icon(
                    icon,
                    size: 18,
                    color: isSelected ? theme.primary : theme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建网格视图文件项
  Widget _buildFileGridItem(BuildContext context, AppTheme theme, FileItem file, int index) {
    final iconColors = _getFileIconColors(theme, file);
    final isPreviewable = _isPreviewableFile(file);
    final hasPreview = file.thumbnailUrl != null && file.thumbnailUrl!.isNotEmpty;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _openFile(file),
        borderRadius: BorderRadius.circular(12),
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          decoration: BoxDecoration(
            color: theme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.outline.withValues(alpha: 0.08),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: theme.shadow.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
            // 为可预览文件添加背景图片
            image: (isPreviewable && hasPreview)
                ? DecorationImage(
                    image: NetworkImage(file.thumbnailUrl!),
                    fit: BoxFit.cover,
                    colorFilter: ColorFilter.mode(
                      Colors.black.withValues(alpha: 0.3),
                      BlendMode.darken,
                    ),
                  )
                : null,
          ),
          child: Column(
            children: [
              // 主要内容区域
              Expanded(
                child: Container(
                  decoration: (isPreviewable && hasPreview)
                      ? BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(12),
                            topRight: Radius.circular(12),
                            bottomLeft: file.isFolder ? Radius.circular(12) : Radius.zero,
                            bottomRight: file.isFolder ? Radius.circular(12) : Radius.zero,
                          ),
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withValues(alpha: 0.7),
                            ],
                            stops: [0.0, 1.0],
                          ),
                        )
                      : null,
                  child: Padding(
                    padding: EdgeInsets.all(12),
                    child: Column(
                      children: [
                        // 顶部：文件图标和类型标签
                        Row(
                          children: [
                            // 文件图标或缩略图（当有预览背景时，显示小图标）
                            if (!(isPreviewable && hasPreview))
                              Hero(
                                tag: 'grid_file_icon_${file.id}',
                                child: Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: iconColors,
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                    boxShadow: [
                                      BoxShadow(
                                        color: iconColors.first.withValues(alpha: 0.2),
                                        blurRadius: 6,
                                        offset: Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    _getFileIcon(file),
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              )
                            else
                              // 当有预览背景时，显示小的文件类型图标
                              Container(
                                padding: EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.5),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  _getFileIcon(file),
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            Spacer(),
                            // 文件夹标签或更多操作
                            if (file.isFolder)
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                decoration: BoxDecoration(
                                  color: (isPreviewable && hasPreview)
                                      ? Colors.white.withValues(alpha: 0.9)
                                      : theme.primary.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(3),
                                ),
                                child: Text(
                                  '文件夹',
                                  style: theme.getTextStyle(
                                    fontSize: 8,
                                    fontWeight: FontWeight.w500,
                                    color: (isPreviewable && hasPreview) ? theme.primary : theme.primary,
                                  ),
                                ),
                              )
                            else
                              Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: () => _showFileOptions(context, file),
                                  borderRadius: BorderRadius.circular(6),
                                  child: Container(
                                    padding: EdgeInsets.all(4),
                                    decoration: (isPreviewable && hasPreview)
                                        ? BoxDecoration(
                                            color: Colors.black.withValues(alpha: 0.3),
                                            borderRadius: BorderRadius.circular(6),
                                          )
                                        : null,
                                    child: Icon(
                                      LucideIcons.ellipsisVertical,
                                      size: 14,
                                      color: (isPreviewable && hasPreview)
                                          ? Colors.white.withValues(alpha: 0.8)
                                          : theme.onSurface.withValues(alpha: 0.4),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                        Spacer(),
                        // 文件名（移到底部，在预览背景上更清晰）
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Container(
                            padding: (isPreviewable && hasPreview)
                                ? EdgeInsets.symmetric(horizontal: 8, vertical: 4)
                                : EdgeInsets.zero,
                            decoration: (isPreviewable && hasPreview)
                                ? BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.6),
                                    borderRadius: BorderRadius.circular(6),
                                  )
                                : null,
                            child: Text(
                              file.name,
                              style: theme.getTextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                color: (isPreviewable && hasPreview) ? Colors.white : theme.onSurface,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        SizedBox(height: 4),
                        // 文件详细信息
                        Row(
                          children: [
                            // 来源标签
                            if (file.source.isNotEmpty) ...[
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                                decoration: BoxDecoration(
                                  color: (isPreviewable && hasPreview)
                                      ? Colors.black.withValues(alpha: 0.5)
                                      : theme.onSurface.withValues(alpha: 0.06),
                                  borderRadius: BorderRadius.circular(3),
                                ),
                                child: Text(
                                  file.source,
                                  style: theme.getTextStyle(
                                    fontSize: 9,
                                    fontWeight: FontWeight.w500,
                                    color: (isPreviewable && hasPreview)
                                        ? Colors.white.withValues(alpha: 0.9)
                                        : theme.onSurface.withValues(alpha: 0.6),
                                  ),
                                ),
                              ),
                              SizedBox(width: 4),
                            ],
                            // 文件大小
                            if (file.size != null && file.size! > 0) ...[
                              FileSizeDisplay(
                                size: file.size!,
                                theme: theme,
                                fontSize: 10,
                                iconSize: 10,
                                spacing: 2,
                              ),
                            ],
                          ],
                        ),
                        SizedBox(height: 4),
                        // 修改时间
                        Row(
                          children: [
                            Icon(
                              LucideIcons.clock,
                              size: 10,
                              color: (isPreviewable && hasPreview)
                                  ? Colors.white.withValues(alpha: 0.8)
                                  : theme.onSurface.withValues(alpha: 0.5),
                            ),
                            SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                _formatDate(file.date),
                                style: theme.getTextStyle(
                                  fontSize: 10,
                                  color: (isPreviewable && hasPreview)
                                      ? Colors.white.withValues(alpha: 0.8)
                                      : theme.onSurface.withValues(alpha: 0.6),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // 底部快捷操作栏（仅对文件显示）
              if (!file.isFolder)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                  decoration: BoxDecoration(
                    color: theme.onSurface.withValues(alpha: 0.03),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      QuickActionButton(
                        icon: LucideIcons.share2,
                        onTap: () => _shareFile(file),
                        theme: theme,
                        padding: EdgeInsets.all(4),
                        borderRadius: 4,
                      ),
                      Container(
                        width: 1,
                        height: 12,
                        color: theme.outline.withValues(alpha: 0.1),
                      ),
                      QuickActionButton(
                        icon: LucideIcons.download,
                        onTap: () => _downloadFile(file),
                        theme: theme,
                        padding: EdgeInsets.all(4),
                        borderRadius: 4,
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建面包屑导航
  Widget _buildBreadcrumbNavigation(BuildContext context, AppTheme theme) {
    return Obx(() {
      if (!controller.isInFolder.value) {
        return SizedBox.shrink();
      }

      return Container(
        height: 48,
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Row(
          children: [
            // 返回按钮
            GestureDetector(
              onTap: () => controller.goBack(),
              child: Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  LucideIcons.chevronLeft,
                  size: 16,
                  color: theme.primary,
                ),
              ),
            ),
            SizedBox(width: 8),

            // 面包屑路径
            Expanded(
              child: BreadcrumbScrollView(
                controller: controller,
                theme: theme,
                onBreadcrumbTap: (index) => controller.navigateToPath(index),
                onRootTap: () {
                  controller.currentPath.clear();
                  controller.isInFolder.value = false;
                  controller.currentFolderName.value = '';
                  controller.loadFiles();
                },
              ),
            ),
          ],
        ),
      );
    });
  }
}
