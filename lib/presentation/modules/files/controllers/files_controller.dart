import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// 文件类型枚举
enum FileType {
  all('全部'),
  myResources('我的资源'),
  myCreated('我创建的'),
  myUploaded('我上传的'),
  myShared('我分享的'),
  recent('来自'),
  document('文档'),
  image('图片'),
  video('视频'),
  audio('音频'),
  archive('压缩包'),
  pdf('PDF'),
  other('其他');

  const FileType(this.label);
  final String label;
}

/// 文件项数据模型
class FileItem {
  final String id;
  final String name;
  final String source;
  final DateTime date;
  final String? icon;
  final bool isFolder;
  final int? size;
  final String? thumbnailUrl;

  FileItem({
    required this.id,
    required this.name,
    required this.source,
    required this.date,
    this.icon,
    this.isFolder = false,
    this.size,
    this.thumbnailUrl,
  });

  /// 获取文件扩展名
  String get extension {
    if (isFolder) return '';
    final lastDot = name.lastIndexOf('.');
    if (lastDot == -1) return '';
    return name.substring(lastDot + 1);
  }

  /// 从JSON创建FileItem对象
  factory FileItem.fromJson(Map<String, dynamic> json) {
    return FileItem(
      id: json['id'] as String,
      name: json['name'] as String,
      source: json['source'] as String,
      date: DateTime.parse(json['date'] as String),
      icon: json['icon'] as String?,
      isFolder: json['isFolder'] as bool? ?? false,
      size: json['size'] as int?,
      thumbnailUrl: json['thumbnailUrl'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'source': source,
      'date': date.toIso8601String(),
      'icon': icon,
      'isFolder': isFolder,
      'size': size,
      'thumbnailUrl': thumbnailUrl,
    };
  }
}

class FilesController extends GetxController {
  /// 当前选中的文件类型
  final selectedFileType = FileType.all.obs;
  // 搜索框展开状态
  final isSearchExpanded = false.obs;

  /// 搜索关键词
  final searchKeyword = ''.obs;

  /// 是否正在搜索
  final isSearching = false.obs;

  /// 文件列表
  final fileList = <FileItem>[].obs;

  /// 是否正在加载
  final isLoading = false.obs;

  /// 排序方式
  final sortBy = 'name'.obs; // 'name', 'date', 'size'

  /// 排序顺序 (true: 升序, false: 降序)
  final isAscending = true.obs;

  /// 视图模式
  final viewMode = 'list'.obs; // 'list', 'grid'

  /// 当前路径
  final currentPath = <String>[].obs;

  /// 当前文件夹名称
  final currentFolderName = ''.obs;

  /// 是否在文件夹内
  final isInFolder = false.obs;

  /// 根目录文件数据
  List<FileItem> _rootFiles = [];

  /// 文件夹内容数据
  Map<String, List<FileItem>> _folderContents = {};

  @override
  void onInit() {
    super.onInit();
    _loadMockDataFromJson();
  }

  /// 切换文件类型
  void changeFileType(FileType type) {
    selectedFileType.value = type;
    loadFiles();
  }

  /// 搜索文件
  void searchFiles(String keyword) {
    searchKeyword.value = keyword;
    loadFiles();
  }

  /// 加载文件列表
  void loadFiles() {
    isLoading.value = true;

    // 如果有搜索关键词，显示搜索状态
    if (searchKeyword.value.isNotEmpty) {
      isSearching.value = true;
    }

    // 获取当前目录的文件列表
    List<FileItem> currentFiles = isInFolder.value ? _folderContents[currentFolderName.value] ?? [] : _rootFiles;

    // 根据搜索关键词过滤文件
    List<FileItem> filteredFiles = currentFiles;

    if (searchKeyword.value.isNotEmpty) {
      // 支持多关键词搜索（空格分隔）
      final keywords = searchKeyword.value.toLowerCase().trim().split(RegExp(r'\s+'));
      filteredFiles = currentFiles.where((file) {
        final fileName = file.name.toLowerCase();
        // 任一关键词匹配即可
        return keywords.any((keyword) => fileName.contains(keyword));
      }).toList();
    }

    // 根据文件类型过滤
    if (selectedFileType.value != FileType.all) {
      filteredFiles = filteredFiles.where((file) {
        switch (selectedFileType.value) {
          case FileType.document:
            return ['pdf', 'doc', 'docx', 'txt', 'ppt', 'pptx', 'xls', 'xlsx'].contains(file.extension.toLowerCase());
          case FileType.image:
            return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].contains(file.extension.toLowerCase());
          case FileType.video:
            return ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'].contains(file.extension.toLowerCase());
          case FileType.audio:
            return ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'].contains(file.extension.toLowerCase());
          case FileType.archive:
            return ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].contains(file.extension.toLowerCase());
          default:
            return true;
        }
      }).toList();
    }

    // 应用排序
    _applySorting(filteredFiles);

    fileList.value = filteredFiles;
    isLoading.value = false;
    isSearching.value = false;
  }

  /// 应用排序
  void _applySorting(List<FileItem> files) {
    switch (sortBy.value) {
      case 'name':
        files.sort((a, b) => isAscending.value
            ? a.name.toLowerCase().compareTo(b.name.toLowerCase())
            : b.name.toLowerCase().compareTo(a.name.toLowerCase()));
        break;
      case 'date':
        files.sort((a, b) => isAscending.value ? a.date.compareTo(b.date) : b.date.compareTo(a.date));
        break;
      case 'size':
        files.sort(
            (a, b) => isAscending.value ? (a.size ?? 0).compareTo(b.size ?? 0) : (b.size ?? 0).compareTo(a.size ?? 0));
        break;
    }
  }

  /// 切换搜索框展开状态
  void toggleSearchExpanded() {
    isSearchExpanded.value = !isSearchExpanded.value;
  }

  /// 从JSON文件加载模拟数据
  Future<void> _loadMockDataFromJson() async {
    try {
      isLoading.value = true;

      // 读取JSON文件
      final String jsonString = await rootBundle.loadString('assets/mock_files_data.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);

      // 解析根目录文件
      final List<dynamic> rootFilesJson = jsonData['rootFiles'] as List<dynamic>;
      _rootFiles = rootFilesJson.map((json) => FileItem.fromJson(json as Map<String, dynamic>)).toList();

      // 解析文件夹内容
      final Map<String, dynamic> folderContentsJson = jsonData['folderContents'] as Map<String, dynamic>;
      _folderContents = {};

      folderContentsJson.forEach((folderId, filesJson) {
        final List<dynamic> filesList = filesJson as List<dynamic>;
        _folderContents[folderId] = filesList.map((json) => FileItem.fromJson(json as Map<String, dynamic>)).toList();
      });

      // 设置初始文件列表
      fileList.value = _rootFiles;
      isLoading.value = false;
    } catch (e) {
      isLoading.value = false;
      // 如果加载失败，使用空列表
      _rootFiles = [];
      _folderContents = {};
      fileList.value = [];
    }
  }

  /// 切换排序方式
  void changeSortBy(String newSortBy) {
    sortBy.value = newSortBy;
    loadFiles();
  }

  /// 切换排序顺序
  void toggleSortOrder() {
    isAscending.value = !isAscending.value;
    loadFiles();
  }

  /// 切换视图模式
  void changeViewMode(String newViewMode) {
    viewMode.value = newViewMode;
  }

  /// 进入文件夹
  void enterFolder(FileItem folder) {
    if (!folder.isFolder) return;

    currentPath.add(folder.name);
    currentFolderName.value = folder.name;
    isInFolder.value = true;

    // 这里可以加载文件夹内的文件
    _loadFolderFiles(folder.id);
  }

  /// 返回上级目录
  void goBack() {
    if (currentPath.isEmpty) return;

    currentPath.removeLast();

    if (currentPath.isEmpty) {
      isInFolder.value = false;
      currentFolderName.value = '';
      loadFiles(); // 回到根目录，加载原始文件列表
    } else {
      currentFolderName.value = currentPath.last;
      // 加载上级目录的文件
      _loadFolderFilesByPath();
    }
  }

  /// 导航到指定路径层级
  void navigateToPath(int index) {
    if (index < 0 || index >= currentPath.length) return;

    // 移除指定索引之后的所有路径
    currentPath.removeRange(index + 1, currentPath.length);

    if (currentPath.isEmpty) {
      isInFolder.value = false;
      currentFolderName.value = '';
      loadFiles();
    } else {
      currentFolderName.value = currentPath.last;
      // 根据路径加载对应文件夹的内容
      _loadFolderFilesByPath();
    }
  }

  /// 根据当前路径加载文件夹内容
  void _loadFolderFilesByPath() {
    if (currentPath.isEmpty) {
      loadFiles();
      return;
    }

    // 根据路径层级找到对应的文件夹ID
    String? folderId = _findFolderIdByPath(currentPath);
    if (folderId != null) {
      _loadFolderFiles(folderId);
    } else {
      // 如果找不到对应路径，回到根目录
      currentPath.clear();
      isInFolder.value = false;
      currentFolderName.value = '';
      loadFiles();
    }
  }

  /// 根据路径查找文件夹ID
  String? _findFolderIdByPath(List<String> path) {
    if (path.isEmpty) return null;

    // 从根目录开始查找
    List<FileItem> currentFiles = _rootFiles;
    String? currentFolderId;

    for (String pathName in path) {
      // 在当前层级查找匹配的文件夹
      FileItem? folder = currentFiles.firstWhereOrNull(
        (file) => file.isFolder && file.name == pathName,
      );

      if (folder == null) return null;

      currentFolderId = folder.id;
      // 获取下一层级的文件列表
      currentFiles = _folderContents[folder.id] ?? [];
    }

    return currentFolderId;
  }

  /// 加载文件夹内的文件（从JSON数据）
  void _loadFolderFiles(String folderId) {
    isLoading.value = true;

    // 模拟加载文件夹内容
    Future.delayed(Duration(milliseconds: 500), () {
      // 从JSON数据中获取文件夹内容
      fileList.value = _folderContents[folderId] ?? [];
      isLoading.value = false;
    });
  }
}
