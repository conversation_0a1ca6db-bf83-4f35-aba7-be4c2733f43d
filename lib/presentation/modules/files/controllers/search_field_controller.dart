import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// 搜索框状态管理控制器
class SearchFieldController extends GetxController with GetTickerProviderStateMixin {
  // 响应式状态
  final RxBool isFocused = false.obs;
  final RxString searchKeyword = ''.obs;
  final RxBool showClearButton = false.obs;
  final RxBool isListening = false.obs;

  // 动画控制器
  late AnimationController focusAnimationController;
  late AnimationController clearAnimationController;
  late Animation<double> focusAnimation;
  late Animation<double> clearAnimation;

  // 文本控制器和焦点节点
  final TextEditingController textController = TextEditingController();
  final FocusNode focusNode = FocusNode();

  @override
  void onInit() {
    super.onInit();
    _initializeAnimations();
    _setupListeners();
  }

  void _initializeAnimations() {
    focusAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    clearAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    focusAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      parent: focusAnimationController,
      curve: Curves.easeOutCubic,
    ));

    clearAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      parent: clearAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  void _setupListeners() {
    // 焦点变化监听
    focusNode.addListener(_onFocusChange);

    // 搜索关键词变化监听
    ever(searchKeyword, (value) {
      showClearButton.value = value.isNotEmpty;
      if (value.isNotEmpty) {
        clearAnimationController.forward();
      } else {
        clearAnimationController.reverse();
      }
    });
  }

  void _onFocusChange() {
    isFocused.value = focusNode.hasFocus;
    if (isFocused.value) {
      focusAnimationController.forward();
    } else {
      focusAnimationController.reverse();
    }
  }

  // 业务方法
  void updateSearchKeyword(String value) {
    searchKeyword.value = value;
    // 同步更新文本控制器（避免循环调用）
    if (textController.text != value) {
      textController.text = value;
    }
  }

  void clearSearch() {
    searchKeyword.value = '';
    textController.clear();
    focusNode.requestFocus();
    HapticFeedback.lightImpact();
  }

  void requestFocus() {
    focusNode.requestFocus();
  }

  /// 重置搜索状态和动画（用于新的搜索会话）
  void resetForNewSearch() {
    // 重置动画状态到初始位置
    focusAnimationController.reset();
    clearAnimationController.reset();

    // 清空搜索内容
    searchKeyword.value = '';
    textController.clear();

    // 重置焦点状态
    isFocused.value = false;
    isListening.value = false;
  }

  // 语音搜索相关方法
  void startListening() {
    if (!isListening.value) {
      isListening.value = true;
      HapticFeedback.mediumImpact();
      // TODO: 集成语音识别服务
      _simulateVoiceRecognition();
    }
  }

  void stopListening() {
    if (isListening.value) {
      isListening.value = false;
      HapticFeedback.lightImpact();
      // TODO: 停止语音识别服务
    }
  }

  // 模拟语音识别过程（用于演示）
  void _simulateVoiceRecognition() {
    // 模拟语音识别延迟
    Future.delayed(const Duration(seconds: 2), () {
      if (isListening.value) {
        // 模拟识别结果
        updateSearchKeyword('语音搜索结果');
        stopListening();
      }
    });
  }

  @override
  void onClose() {
    focusAnimationController.dispose();
    clearAnimationController.dispose();
    focusNode.removeListener(_onFocusChange);
    focusNode.dispose();
    textController.dispose();
    super.onClose();
  }
}
