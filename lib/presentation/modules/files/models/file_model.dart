enum FileStatus { uploading, uploaded, failed, deleted }

class FileModel {
  final String id;
  final String name;
  final String originalName;
  final String mimeType;
  final int size;
  final String path;
  final String? url;
  final FileStatus status;
  final DateTime uploadedAt;
  final String uploadedBy;
  final String? taskId;
  final String? submitterName;
  final String? submitterEmail;
  final String? checksum;
  final Map<String, dynamic>? metadata;

  FileModel({
    required this.id,
    required this.name,
    required this.originalName,
    required this.mimeType,
    required this.size,
    required this.path,
    this.url,
    required this.status,
    required this.uploadedAt,
    required this.uploadedBy,
    this.taskId,
    this.submitterName,
    this.submitterEmail,
    this.checksum,
    this.metadata,
  });

  factory FileModel.fromJson(Map<String, dynamic> json) {
    return FileModel(
      id: json['id'],
      name: json['name'],
      originalName: json['original_name'],
      mimeType: json['mime_type'],
      size: json['size'],
      path: json['path'],
      url: json['url'],
      status: FileStatus.values.byName(json['status']),
      uploadedAt: DateTime.parse(json['uploaded_at']),
      uploadedBy: json['uploaded_by'],
      taskId: json['task_id'],
      submitterName: json['submitter_name'],
      submitterEmail: json['submitter_email'],
      checksum: json['checksum'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'original_name': originalName,
      'mime_type': mimeType,
      'size': size,
      'path': path,
      'url': url,
      'status': status.name,
      'uploaded_at': uploadedAt.toIso8601String(),
      'uploaded_by': uploadedBy,
      'task_id': taskId,
      'submitter_name': submitterName,
      'submitter_email': submitterEmail,
      'checksum': checksum,
      'metadata': metadata,
    };
  }

  // 获取文件大小的可读格式
  String get sizeFormatted {
    if (size < 1024) {
      return '$size B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)} KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  // 获取文件状态文本
  String get statusText {
    switch (status) {
      case FileStatus.uploading:
        return '上传中';
      case FileStatus.uploaded:
        return '已上传';
      case FileStatus.failed:
        return '上传失败';
      case FileStatus.deleted:
        return '已删除';
    }
  }

  // 获取文件扩展名
  String get extension {
    final parts = originalName.split('.');
    return parts.length > 1 ? parts.last.toLowerCase() : '';
  }

  // 判断是否为图片文件
  bool get isImage {
    return mimeType.startsWith('image/') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  // 判断是否为视频文件
  bool get isVideo {
    return mimeType.startsWith('video/') || ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].contains(extension);
  }

  // 判断是否为音频文件
  bool get isAudio {
    return mimeType.startsWith('audio/') || ['mp3', 'wav', 'flac', 'aac', 'ogg'].contains(extension);
  }

  // 判断是否为文档文件
  bool get isDocument {
    return ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].contains(extension);
  }

  // 判断是否为表格文件
  bool get isSpreadsheet {
    return ['xls', 'xlsx', 'csv', 'ods'].contains(extension);
  }

  // 判断是否为演示文稿
  bool get isPresentation {
    return ['ppt', 'pptx', 'odp'].contains(extension);
  }

  // 判断是否为压缩文件
  bool get isArchive {
    return ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].contains(extension);
  }

  // 获取文件类型图标
  String get typeIcon {
    if (isImage) return '🖼️';
    if (isVideo) return '🎥';
    if (isAudio) return '🎵';
    if (isDocument) return '📄';
    if (isSpreadsheet) return '📊';
    if (isPresentation) return '📽️';
    if (isArchive) return '🗜️';
    return '📁';
  }

  // 创建副本并更新属性
  FileModel copyWith({
    String? id,
    String? name,
    String? originalName,
    String? mimeType,
    int? size,
    String? path,
    String? url,
    FileStatus? status,
    DateTime? uploadedAt,
    String? uploadedBy,
    String? taskId,
    String? submitterName,
    String? submitterEmail,
    String? checksum,
    Map<String, dynamic>? metadata,
  }) {
    return FileModel(
      id: id ?? this.id,
      name: name ?? this.name,
      originalName: originalName ?? this.originalName,
      mimeType: mimeType ?? this.mimeType,
      size: size ?? this.size,
      path: path ?? this.path,
      url: url ?? this.url,
      status: status ?? this.status,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      taskId: taskId ?? this.taskId,
      submitterName: submitterName ?? this.submitterName,
      submitterEmail: submitterEmail ?? this.submitterEmail,
      checksum: checksum ?? this.checksum,
      metadata: metadata ?? this.metadata,
    );
  }
}
