import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:dotted_border/dotted_border.dart';
import '../controllers/send_controller.dart';
import '../../../../core/theme/theme_helper.dart';
import '../../../global_widgets/form_section_header.dart';

class SendPage extends GetView<SendController> {
  const SendPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Material(
      color: Colors.transparent, // 透明背景，不影响主题
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            title: Text('NiceXfer'),
            centerTitle: true,
          ),
        ],
        body: SingleChildScrollView(
          child: Padding(
            padding: AppTheme.paddingAll16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 文件选择区域
                _buildFileSelectionArea(context, theme),
                SizedBox(height: AppTheme.spacingVertical24),

                // 接收者信息
                _buildRecipientSection(context, theme),
                SizedBox(height: AppTheme.spacingVertical24),

                // 高级设置
                _buildAdvancedSettings(context, theme),
                SizedBox(height: AppTheme.spacingVertical32),

                // 发送按钮
                _buildSendButton(context, theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFileSelectionArea(BuildContext context, AppTheme theme) {
    return Obx(() => DottedBorder(
          options: RoundedRectDottedBorderOptions(
            dashPattern: const [8, 4],
            strokeWidth: 2,
            radius: const Radius.circular(12),
            color: theme.outline,
            padding: const EdgeInsets.all(0),
          ),
          child: Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: AppTheme.borderRadius12,
              color: theme.surfaceContainerHighest,
            ),
            child: controller.selectedFiles.isEmpty
                ? _buildEmptyFileArea(context, theme)
                : _buildSelectedFilesList(context, theme),
          ),
        ));
  }

  Widget _buildEmptyFileArea(BuildContext context, AppTheme theme) {
    return InkWell(
      onTap: controller.selectFiles,
      borderRadius: AppTheme.borderRadius12,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.cloud_upload_outlined,
            size: 48,
            color: theme.onSurfaceVariant,
          ),
          SizedBox(height: AppTheme.spacingVertical16),
          Text(
            '点击选择文件或拖拽文件到此处',
            style: theme.cardTitleStyle,
          ),
          SizedBox(height: AppTheme.spacingVertical8),
          Text(
            '支持多种文件格式，最大单文件 100MB',
            style: theme.cardSubtitleStyle,
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedFilesList(BuildContext context, AppTheme theme) {
    return ListView.builder(
      padding: AppTheme.paddingAll16,
      itemCount: controller.selectedFiles.length + 1,
      itemBuilder: (context, index) {
        if (index == controller.selectedFiles.length) {
          return _buildAddMoreFilesButton(context, theme);
        }

        final filePath = controller.selectedFiles[index];
        final fileName = filePath.split('/').last;

        return Container(
          margin: AppTheme.paddingOnlyBottom8,
          padding: AppTheme.paddingAll12,
          decoration: theme.fileItemDecoration,
          child: Row(
            children: [
              Icon(Icons.insert_drive_file, color: theme.primary),
              SizedBox(width: AppTheme.spacingHorizontal12),
              Expanded(
                child: Text(
                  fileName,
                  style: theme.getTextStyle(
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              IconButton(
                icon: Icon(Icons.close, size: 20, color: theme.onSurfaceVariant),
                onPressed: () => controller.removeFile(filePath),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAddMoreFilesButton(BuildContext context, AppTheme theme) {
    return Container(
      margin: AppTheme.paddingOnlyTop8,
      child: OutlinedButton.icon(
        onPressed: controller.selectFiles,
        icon: const Icon(Icons.add),
        label: const Text('添加更多文件'),
        style: theme.outlinedButtonStyle,
      ),
    );
  }

  Widget _buildRecipientSection(BuildContext context, AppTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormSectionHeader(
          icon: Icons.person_outline,
          title: '接收者信息',
        ),
        SizedBox(height: AppTheme.spacingVertical12),
        TextField(
          onChanged: controller.setRecipientEmail,
          decoration: theme.getInputDecoration(
            labelText: '接收者邮箱',
            hintText: '请输入接收者的邮箱地址',
            prefixIcon: Icon(Icons.email_outlined, color: theme.onSurfaceVariant),
          ),
          keyboardType: TextInputType.emailAddress,
        ),
      ],
    );
  }

  Widget _buildAdvancedSettings(BuildContext context, AppTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormSectionHeader(
          icon: Icons.settings_outlined,
          title: '高级设置',
          subtitle: '配置链接有效期和安全选项',
        ),
        SizedBox(height: AppTheme.spacingVertical20),

        // 链接有效期
        Container(
          padding: AppTheme.paddingAll16,
          decoration: theme.formGroupDecoration,
          child: Row(
            children: [
              Icon(Icons.schedule, color: theme.primary, size: 20),
              SizedBox(width: AppTheme.spacingHorizontal12),
              Expanded(
                child: Text(
                  '链接有效期',
                  style: theme.getTextStyle(
                    fontSize: AppTheme.fontSize14,
                    fontWeight: AppTheme.fontWeightMedium,
                  ),
                ),
              ),
              Obx(() => DropdownButton<int>(
                    value: controller.linkExpiration,
                    underline: Container(),
                    items: const [
                      DropdownMenuItem(value: 1, child: Text('1小时')),
                      DropdownMenuItem(value: 6, child: Text('6小时')),
                      DropdownMenuItem(value: 24, child: Text('24小时')),
                      DropdownMenuItem(value: 72, child: Text('3天')),
                      DropdownMenuItem(value: 168, child: Text('7天')),
                    ],
                    onChanged: (value) => controller.setLinkExpiration(value!),
                  )),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacingVertical16),

        // 密码保护
        Obx(() => Column(
              children: [
                Container(
                  padding: AppTheme.paddingAll16,
                  decoration: theme.formGroupDecoration,
                  child: Row(
                    children: [
                      Icon(Icons.lock_outline, color: theme.primary, size: 20),
                      SizedBox(width: AppTheme.spacingHorizontal12),
                      Expanded(
                        child: Text(
                          '密码保护',
                          style: theme.getTextStyle(
                            fontSize: AppTheme.fontSize14,
                            fontWeight: AppTheme.fontWeightMedium,
                          ),
                        ),
                      ),
                      Switch(
                        value: controller.requirePassword,
                        onChanged: (value) => controller.togglePasswordProtection(),
                        activeColor: theme.primary,
                      ),
                    ],
                  ),
                ),
                if (controller.requirePassword) ...[
                  SizedBox(height: AppTheme.spacingVertical16),
                  TextField(
                    onChanged: controller.setPassword,
                    decoration: theme.getEnhancedInputDecoration(
                      labelText: '设置密码',
                      hintText: '请输入访问密码',
                      prefixIcon: Icon(Icons.lock_outlined, color: theme.primary),
                    ),
                    obscureText: true,
                  ),
                ],
              ],
            )),
      ],
    );
  }

  Widget _buildSendButton(BuildContext context, AppTheme theme) {
    return Obx(() => SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: controller.canSend ? controller.sendFiles : null,
            style: theme.enhancedPrimaryButtonStyle,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.send,
                  size: 20,
                ),
                SizedBox(width: AppTheme.spacingHorizontal8),
                Text(
                  '发送文件',
                  style: theme.getTextStyle(
                    fontSize: AppTheme.fontSize16,
                    fontWeight: AppTheme.fontWeightSemiBold,
                    color: theme.onPrimary,
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}
