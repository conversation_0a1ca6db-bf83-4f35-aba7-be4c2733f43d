import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SendController extends GetxController {
  // 发送文件状态
  final _isUploading = false.obs;
  bool get isUploading => _isUploading.value;

  // 上传进度
  final _uploadProgress = 0.0.obs;
  double get uploadProgress => _uploadProgress.value;

  // 选中的文件列表
  final _selectedFiles = <String>[].obs;
  List<String> get selectedFiles => _selectedFiles;

  // 接收者信息
  final _recipientEmail = ''.obs;
  String get recipientEmail => _recipientEmail.value;

  // 链接有效期（小时）
  final _linkExpiration = 24.obs;
  int get linkExpiration => _linkExpiration.value;

  // 是否需要密码保护
  final _requirePassword = false.obs;
  bool get requirePassword => _requirePassword.value;

  // 密码
  final _password = ''.obs;
  String get password => _password.value;

  // 是否可以发送
  bool get canSend => _selectedFiles.isNotEmpty && _recipientEmail.isNotEmpty && !_isUploading.value;

  // 选择文件
  void selectFiles() {
    // TODO: 实现文件选择逻辑
    debugPrint('选择文件');
  }

  // 移除文件
  void removeFile(String filePath) {
    _selectedFiles.remove(filePath);
  }

  // 设置接收者邮箱
  void setRecipientEmail(String email) {
    _recipientEmail.value = email;
  }

  // 设置链接有效期
  void setLinkExpiration(int hours) {
    _linkExpiration.value = hours;
  }

  // 切换密码保护
  void togglePasswordProtection() {
    _requirePassword.value = !_requirePassword.value;
    if (!_requirePassword.value) {
      _password.value = '';
    }
  }

  // 设置密码
  void setPassword(String pwd) {
    _password.value = pwd;
  }

  // 发送文件
  void sendFiles() {
    createSendTask();
  }

  // 创建发送任务
  void createSendTask() {
    if (_selectedFiles.isEmpty) {
      Get.snackbar('错误', '请选择要发送的文件');
      return;
    }

    if (_recipientEmail.isEmpty) {
      Get.snackbar('错误', '请输入接收者邮箱');
      return;
    }

    _isUploading.value = true;

    // TODO: 实现文件上传和链接生成逻辑
    // 模拟上传进度
    _simulateUpload();
  }

  // 模拟上传进度
  void _simulateUpload() {
    _uploadProgress.value = 0.0;

    // 模拟上传进度更新
    for (int i = 0; i <= 100; i += 10) {
      Future.delayed(Duration(milliseconds: i * 50), () {
        _uploadProgress.value = i / 100;

        if (i == 100) {
          _isUploading.value = false;
          Get.snackbar('成功', '文件发送链接已创建');
          _resetForm();
        }
      });
    }
  }

  // 重置表单
  void _resetForm() {
    _selectedFiles.clear();
    _recipientEmail.value = '';
    _linkExpiration.value = 24;
    _requirePassword.value = false;
    _password.value = '';
    _uploadProgress.value = 0.0;
  }
}
