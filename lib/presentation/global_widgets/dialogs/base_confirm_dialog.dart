import 'package:flutter/material.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 通用确认对话框基础组件
class BaseConfirmDialog extends StatelessWidget {
  final Widget header;
  final Widget content;
  final List<Widget> actions;
  final AppTheme theme;

  const BaseConfirmDialog({
    super.key,
    required this.header,
    required this.content,
    required this.actions,
    required this.theme,
  });

  /// 显示确认对话框
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget header,
    required Widget content,
    required List<Widget> actions,
    required AppTheme theme,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => BaseConfirmDialog(
        header: header,
        content: content,
        actions: actions,
        theme: theme,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(28),
      ),
      backgroundColor: theme.surface,
      elevation: 0,
      shadowColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 400,
          minWidth: 320,
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            header,
            const SizedBox(height: 20),
            content,
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: actions
                  .map((action) => Padding(
                        padding: const EdgeInsets.only(left: 12),
                        child: action,
                      ))
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }
}
