import 'package:flutter/material.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 底部弹窗标题组件
class BottomSheetHeader extends StatelessWidget {
  final AppTheme theme;
  final IconData icon;
  final String title;
  final String? subtitle;
  final String? actionHint;
  final Color? iconColor;
  final Color? backgroundColor;
  final EdgeInsets? padding;
  final bool showBadge;
  final String? badgeText;

  const BottomSheetHeader({
    super.key,
    required this.theme,
    required this.icon,
    required this.title,
    this.subtitle,
    this.actionHint,
    this.iconColor,
    this.backgroundColor,
    this.padding,
    this.showBadge = false,
    this.badgeText,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveIconColor = iconColor ?? theme.primary;
    final effectiveBackgroundColor = backgroundColor ?? theme.primary;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            effectiveBackgroundColor.withValues(alpha: 0.08),
            effectiveBackgroundColor.withValues(alpha: 0.03),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: effectiveBackgroundColor.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Stack(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: effectiveBackgroundColor.withValues(alpha: 0.12),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: effectiveBackgroundColor.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: effectiveIconColor,
                  size: 22,
                ),
              ),
              if (showBadge && badgeText != null)
                Positioned(
                  right: -2,
                  top: -2,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: theme.surface, width: 1.5),
                    ),
                    child: Text(
                      badgeText!,
                      style: theme.getTextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.getTextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: theme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: theme.getTextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: theme.onSurface.withValues(alpha: 0.7),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                if (actionHint != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    actionHint!,
                    style: theme.getTextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w400,
                      color: effectiveBackgroundColor.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
