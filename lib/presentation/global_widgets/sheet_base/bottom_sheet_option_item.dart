import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 底部弹窗选项项组件
class BottomSheetOptionItem extends StatelessWidget {
  final AppTheme theme;
  final IconData icon;
  final String title;
  final String? description;
  final VoidCallback onTap;
  final bool isSelected;
  final bool isDestructive;
  final bool showChevron;
  final Widget? trailing;
  final double iconSize;
  final EdgeInsets? padding;

  const BottomSheetOptionItem({
    super.key,
    required this.theme,
    required this.icon,
    required this.title,
    this.description,
    required this.onTap,
    this.isSelected = false,
    this.isDestructive = false,
    this.showChevron = true,
    this.trailing,
    this.iconSize = 20,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getBorderColor(),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: theme.primary.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            HapticFeedback.selectionClick();
            onTap();
          },
          child: Padding(
            padding: padding ?? const EdgeInsets.all(8),
            child: Row(
              children: [
                _buildIcon(),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildContent(),
                ),
                if (trailing != null)
                  trailing!
                else if (showChevron && !isSelected)
                  Icon(
                    Icons.chevron_right,
                    size: 16,
                    color: theme.onSurface.withValues(alpha: 0.3),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 获取背景颜色
  Color _getBackgroundColor() {
    if (isDestructive) {
      return Colors.red.withValues(alpha: 0.05);
    }
    if (isSelected) {
      return theme.primary.withValues(alpha: 0.08);
    }
    return theme.surface;
  }

  /// 获取边框颜色
  Color _getBorderColor() {
    if (isDestructive) {
      return Colors.red.withValues(alpha: 0.2);
    }
    if (isSelected) {
      return theme.primary.withValues(alpha: 0.2);
    }
    return theme.onSurface.withValues(alpha: 0.08);
  }

  /// 构建图标
  Widget _buildIcon() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: _getIconBackgroundColor(),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        icon,
        size: iconSize,
        color: _getIconColor(),
      ),
    );
  }

  /// 获取图标背景颜色
  Color _getIconBackgroundColor() {
    if (isDestructive) {
      return Colors.red.withValues(alpha: 0.1);
    }
    if (isSelected) {
      return theme.primary.withValues(alpha: 0.15);
    }
    return theme.onSurface.withValues(alpha: 0.05);
  }

  /// 获取图标颜色
  Color _getIconColor() {
    if (isDestructive) {
      return Colors.red;
    }
    if (isSelected) {
      return theme.primary;
    }
    return theme.onSurface.withValues(alpha: 0.7);
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.getTextStyle(
            fontSize: 16,
            color: _getTitleColor(),
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
        if (description != null) ...[
          const SizedBox(height: 2),
          Text(
            description!,
            style: theme.getTextStyle(
              fontSize: 12,
              color: _getDescriptionColor(),
            ),
          ),
        ],
      ],
    );
  }

  /// 获取标题颜色
  Color _getTitleColor() {
    if (isDestructive) {
      return Colors.red;
    }
    if (isSelected) {
      return theme.primary;
    }
    return theme.onSurface;
  }

  /// 获取描述颜色
  Color _getDescriptionColor() {
    if (isDestructive) {
      return Colors.red.withValues(alpha: 0.7);
    }
    return theme.onSurface.withValues(alpha: 0.5);
  }
}
