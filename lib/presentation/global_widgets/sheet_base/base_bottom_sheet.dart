import 'package:flutter/material.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 底部弹窗基础组件
class BaseBottomSheet extends StatelessWidget {
  final AppTheme theme;
  final Widget? header;
  final List<Widget> children;
  final EdgeInsets? padding;

  const BaseBottomSheet({
    super.key,
    required this.theme,
    this.header,
    required this.children,
    this.padding,
  });

  /// 显示底部弹窗的通用方法
  static void show(
    BuildContext context,
    Widget child,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      enableDrag: true,
      builder: (context) => child,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        color: theme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: theme.shadow.withValues(alpha: 0.15),
            blurRadius: 30,
            offset: const Offset(0, -8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: theme.primary.withValues(alpha: 0.05),
            blurRadius: 60,
            offset: const Offset(0, -20),
            spreadRadius: 10,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          _buildDragIndicator(),
          // 标题区域（可选）
          if (header != null) header!,
          // 内容区域
          Padding(
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: children,
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  /// 构建拖拽指示器
  Widget _buildDragIndicator() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      width: 48,
      height: 5,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.primary.withValues(alpha: 0.3),
            theme.primary.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(3),
      ),
    );
  }
}

