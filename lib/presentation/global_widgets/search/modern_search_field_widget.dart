import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';
import 'package:nicexfer_app/presentation/modules/files/controllers/search_field_controller.dart';

/// 纯 UI 组件 - 只负责渲染
class ModernSearchFieldWidget extends StatelessWidget {
  final Function(String)? onChanged;
  final String hintText;
  final bool autofocus;
  final double? height;
  final EdgeInsetsGeometry? margin;
  final bool enableVoiceSearch;

  const ModernSearchFieldWidget({
    super.key,
    this.onChanged,
    this.hintText = '搜索记录、文件名或描述...',
    this.autofocus = true,
    this.height,
    this.margin,
    this.enableVoiceSearch = false,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SearchFieldController>();
    final theme = AppTheme.of(context);

    return Container(
      height: height ?? 56,
      margin: margin,
      child: AnimatedBuilder(
        animation: controller.focusAnimation,
        builder: (context, child) {
          return _buildSearchField(context, controller, theme);
        },
      ),
    );
  }

  Widget _buildSearchField(BuildContext context, SearchFieldController controller, AppTheme theme) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      decoration: _buildDecoration(controller, theme),
      child: TextField(
        controller: controller.textController,
        focusNode: controller.focusNode,
        autofocus: autofocus,
        onChanged: (value) {
          controller.updateSearchKeyword(value);
          onChanged?.call(value);
        },
        style: theme.getTextStyle(
          color: theme.onSurface,
          fontWeight: FontWeight.w500,
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: theme.getTextStyle(
            color: theme.onSurfaceVariant.withValues(
              alpha: theme.isDarkMode ? 0.7 : 0.6,
            ),
            fontWeight: FontWeight.w400,
            fontSize: 16,
          ),
          prefixIcon: _SearchIcon(controller: controller),
          suffixIcon: _ClearButton(controller: controller, enableVoiceSearch: enableVoiceSearch),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  BoxDecoration _buildDecoration(SearchFieldController controller, AppTheme theme) {
    // 使用主题适配的背景颜色
    final baseBackgroundColor = theme.getAdaptiveColor(
      lightColor: theme.surface,
      darkColor: theme.surfaceContainer,
    );
    final focusedBackgroundColor = theme.getAdaptiveColor(
      lightColor: theme.surface,
      darkColor: theme.surfaceContainerHighest,
    );

    return BoxDecoration(
      color: Color.lerp(
        baseBackgroundColor,
        focusedBackgroundColor,
        controller.focusAnimation.value,
      ),
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: theme.adaptiveShadowColor.withValues(
            alpha: theme.isDarkMode
                ? 0.25 + (controller.focusAnimation.value * 0.15)
                : 0.12 + (controller.focusAnimation.value * 0.08),
          ),
          blurRadius: 12 + (controller.focusAnimation.value * 8),
          offset: Offset(0, 4 + (controller.focusAnimation.value * 2)),
        ),
      ],
      border: Border.all(
        color: Color.lerp(
          theme.adaptiveBorderColor.withValues(alpha: 0.3),
          theme.primary.withValues(
            alpha: theme.isDarkMode ? 0.8 : 0.6,
          ),
          controller.focusAnimation.value,
        )!,
        width: 1 + (controller.focusAnimation.value * 0.5),
      ),
    );
  }
}

/// 搜索图标组件
class _SearchIcon extends StatelessWidget {
  final SearchFieldController controller;

  const _SearchIcon({required this.controller});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return AnimatedBuilder(
      animation: controller.focusAnimation,
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color.lerp(
                  theme.primary.withValues(
                    alpha: theme.isDarkMode ? 0.15 : 0.08,
                  ),
                  theme.primary.withValues(
                    alpha: theme.isDarkMode ? 0.2 : 0.12,
                  ),
                  controller.focusAnimation.value,
                )!,
                Color.lerp(
                  theme.secondary.withValues(
                    alpha: theme.isDarkMode ? 0.15 : 0.08,
                  ),
                  theme.secondary.withValues(
                    alpha: theme.isDarkMode ? 0.2 : 0.12,
                  ),
                  controller.focusAnimation.value,
                )!,
              ],
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Transform.scale(
            scale: 1.0 + (controller.focusAnimation.value * 0.1),
            child: Icon(
              LucideIcons.search,
              color: Color.lerp(
                theme.primary.withValues(
                  alpha: theme.isDarkMode ? 0.9 : 0.8,
                ),
                theme.primary,
                controller.focusAnimation.value,
              ),
              size: 20,
            ),
          ),
        );
      },
    );
  }
}

/// 清空按钮组件
class _ClearButton extends StatelessWidget {
  final SearchFieldController controller;
  final bool enableVoiceSearch;

  const _ClearButton({
    required this.controller,
    required this.enableVoiceSearch,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!controller.showClearButton.value) {
        return enableVoiceSearch ? _VoiceSearchButton(controller: controller) : const SizedBox.shrink();
      }

      return AnimatedBuilder(
        animation: controller.clearAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: controller.clearAnimation.value,
            child: _buildClearButton(context),
          );
        },
      );
    });
  }

  Widget _buildClearButton(BuildContext context) {
    final theme = AppTheme.of(context);

    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: controller.clearSearch,
          splashColor: theme.rippleColor,
          highlightColor: theme.hoverColor,
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: theme.onSurface.withValues(
                alpha: theme.isDarkMode ? 0.12 : 0.08,
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              LucideIcons.x,
              color: theme.onSurface.withValues(
                alpha: theme.isDarkMode ? 0.8 : 0.6,
              ),
              size: 18,
            ),
          ),
        ),
      ),
    );
  }
}

/// 语音搜索按钮组件
class _VoiceSearchButton extends StatelessWidget {
  final SearchFieldController controller;

  const _VoiceSearchButton({required this.controller});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return AnimatedBuilder(
      animation: controller.focusAnimation,
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.only(right: 8),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () => _handleVoiceSearch(context),
              splashColor: theme.rippleColor,
              highlightColor: theme.hoverColor,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color.lerp(
                        theme.primary.withValues(
                          alpha: theme.isDarkMode ? 0.15 : 0.08,
                        ),
                        theme.primary.withValues(
                          alpha: theme.isDarkMode ? 0.2 : 0.12,
                        ),
                        controller.focusAnimation.value,
                      )!,
                      Color.lerp(
                        theme.secondary.withValues(
                          alpha: theme.isDarkMode ? 0.15 : 0.08,
                        ),
                        theme.secondary.withValues(
                          alpha: theme.isDarkMode ? 0.2 : 0.12,
                        ),
                        controller.focusAnimation.value,
                      )!,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Transform.scale(
                  scale: 1.0 + (controller.focusAnimation.value * 0.05),
                  child: Obx(() => Icon(
                        controller.isListening.value ? LucideIcons.micOff : LucideIcons.mic,
                        color: Color.lerp(
                          theme.primary.withValues(
                            alpha: theme.isDarkMode ? 0.85 : 0.7,
                          ),
                          theme.primary,
                          controller.focusAnimation.value,
                        ),
                        size: 18,
                      )),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleVoiceSearch(BuildContext context) {
    if (controller.isListening.value) {
      controller.stopListening();
    } else {
      controller.startListening();
    }
  }
}
