# 全局组件库

本目录包含可在整个应用中复用的通用组件。

## FormSectionHeader 组件

表单节标题组件，用于显示带图标的节标题和副标题。

### 功能特性

- 支持自定义图标和标题文本
- 可选的副标题显示
- 自定义图标颜色和大小
- 可调节标题和副标题之间的间距
- 自动应用主题样式

### 使用方法

```dart
import '../../../global_widgets/form_section_header.dart';

// 基本使用
FormSectionHeader(
  icon: Icons.settings_outlined,
  title: '高级设置',
  subtitle: '配置链接有效期和安全选项',
)

// 自定义样式
FormSectionHeader(
  icon: Icons.info_outline,
  title: '基本信息',
  subtitle: '设置收集任务的基本信息和截止时间',
  iconColor: Colors.blue,
  iconSize: 28,
  spacing: 12,
)

// 仅显示标题
FormSectionHeader(
  icon: Icons.security,
  title: '安全设置',
)
```

### 参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| icon | IconData | 是 | - | 显示的图标 |
| title | String | 是 | - | 标题文本 |
| subtitle | String? | 否 | null | 副标题文本 |
| iconColor | Color? | 否 | theme.primary | 图标颜色 |
| iconSize | double | 否 | 24 | 图标大小 |
| spacing | double | 否 | 8 | 标题和副标题间距 |

### 已应用的页面

- `SendPage` - 高级设置节
- `CollectPage` - 基本信息节

### 设计原则

- 遵循应用主题设计规范
- 保持一致的视觉风格
- 支持灵活的自定义选项
- 易于维护和扩展