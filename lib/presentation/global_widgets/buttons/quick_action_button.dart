import 'package:flutter/material.dart';

import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 快捷操作按钮组件
/// 用于文件列表和网格视图中的快捷操作（如分享、下载等）
class QuickActionButton extends StatelessWidget {
  /// 按钮图标
  final IconData icon;

  /// 点击回调
  final VoidCallback onTap;

  /// 应用主题
  final AppTheme theme;

  /// 图标大小
  final double iconSize;

  /// 内边距
  final EdgeInsets padding;

  /// 边框圆角
  final double borderRadius;

  /// 是否为网格视图样式
  final bool isGridStyle;

  const QuickActionButton({
    super.key,
    required this.icon,
    required this.onTap,
    required this.theme,
    this.iconSize = 16,
    this.padding = const EdgeInsets.all(8),
    this.borderRadius = 6,
    this.isGridStyle = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isGridStyle) {
      return _buildGridStyle();
    }
    return _buildListStyle();
  }

  /// 构建列表视图样式的快捷操作按钮
  Widget _buildListStyle() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius),
        child: Container(
          padding: padding,
          decoration: BoxDecoration(
            color: theme.onSurface.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              color: theme.outline.withValues(alpha: 0.1),
              width: 0.5,
            ),
          ),
          child: Icon(
            icon,
            size: iconSize,
            color: theme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ),
    );
  }

  /// 构建网格视图样式的快捷操作按钮
  Widget _buildGridStyle() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: Icon(
            icon,
            size: 12,
            color: theme.isDarkMode
                ? theme.onSurface.withValues(alpha: 0.8)
                : theme.surfaceContainer.withValues(alpha: 0.8),
          ),
        ),
      ),
    );
  }
}
