# 头部操作按钮组件

## 概述

`UnifiedHeaderActionButton` 是一个统一的头部操作按钮组件，整合了原有的 `HeaderActionButton` 和 `HeaderActionButtonWidget` 的功能，提供更灵活的自定义选项和更好的一致性。

## 主要特性

- **统一接口**: 整合了两个原有组件的所有功能
- **多种样式**: 支持经典样式和现代样式
- **状态支持**: 支持激活状态和主要按钮状态
- **动画效果**: 可选的平滑动画过渡
- **高度自定义**: 支持自定义颜色、大小、边框等
- **向后兼容**: 提供别名确保现有代码正常工作

## 使用方法

### 基本用法

```dart
// 默认样式
UnifiedHeaderActionButton(
  icon: LucideIcons.search,
  onPressed: () => print('搜索'),
)
```

### 经典样式（类似原 HeaderActionButton）

```dart
UnifiedHeaderActionButton.classic(
  icon: LucideIcons.search,
  onPressed: () => print('搜索'),
  size: 40,
  iconSize: 20,
  showShadow: true,
)
```

### 现代样式（类似原 HeaderActionButtonWidget）

```dart
UnifiedHeaderActionButton.modern(
  icon: LucideIcons.search,
  onPressed: () => print('搜索'),
  isActive: true,
  isPrimary: false,
)
```

### 高级自定义

```dart
UnifiedHeaderActionButton(
  icon: LucideIcons.settings,
  onPressed: () => print('设置'),
  size: 48,
  iconSize: 24,
  backgroundColor: Colors.blue.withValues(alpha:0.1),
  iconColor: Colors.blue,
  borderRadius: 16,
  showShadow: true,
  showBorder: true,
  borderColor: Colors.blue.withValues(alpha:0.3),
  enableAnimation: true,
  animationDuration: Duration(milliseconds: 300),
)
```

## 参数说明

### 基础参数

- `icon`: 按钮图标 (必需)
- `onPressed`: 点击回调
- `size`: 按钮大小 (默认: 40)
- `iconSize`: 图标大小 (默认: 20)

### 样式参数

- `backgroundColor`: 背景颜色
- `iconColor`: 图标颜色
- `borderRadius`: 边框圆角 (默认: 12)
- `padding`: 内边距

### 状态参数

- `isActive`: 是否为激活状态
- `isPrimary`: 是否为主要按钮

### 视觉效果参数

- `showShadow`: 是否显示阴影 (默认: true)
- `showBorder`: 是否显示边框 (默认: false)
- `borderWidth`: 边框宽度 (默认: 1.0)
- `borderColor`: 边框颜色

### 动画参数

- `enableAnimation`: 是否启用动画 (默认: true)
- `animationDuration`: 动画持续时间 (默认: 200ms)

### 样式类型

- `style`: 按钮样式类型 (`HeaderButtonStyle.classic` 或 `HeaderButtonStyle.modern`)

## 迁移指南

### 从 HeaderActionButton 迁移

**原代码:**

```dart
HeaderActionButton(
  icon: LucideIcons.search,
  onPressed: () => print('搜索'),
  theme: theme,
  size: 40,
  iconSize: 20,
)
```

**新代码:**

```dart
UnifiedHeaderActionButton.classic(
  icon: LucideIcons.search,
  onPressed: () => print('搜索'),
  size: 40,
  iconSize: 20,
)
```

### 从 HeaderActionButtonWidget 迁移

**原代码:**

```dart
HeaderActionButtonWidget(
  icon: LucideIcons.search,
  onPressed: () => print('搜索'),
  isActive: true,
  isPrimary: false,
)
```

**新代码:**

```dart
UnifiedHeaderActionButton.modern(
  icon: LucideIcons.search,
  onPressed: () => print('搜索'),
  isActive: true,
  isPrimary: false,
)
```

## 设计原则

1. **一致性**: 统一的接口和行为
2. **灵活性**: 支持多种自定义选项
3. **性能**: 优化的渲染和动画
4. **可维护性**: 清晰的代码结构和文档
5. **向后兼容**: 确保现有代码正常工作

## 注意事项

- 使用 `.classic()` 构造函数获得类似原 `HeaderActionButton` 的行为
- 使用 `.modern()` 构造函数获得类似原 `HeaderActionButtonWidget` 的行为
- 自定义颜色参数具有最高优先级，会覆盖样式计算的颜色
- 动画效果在现代样式中默认启用，在经典样式中默认禁用
