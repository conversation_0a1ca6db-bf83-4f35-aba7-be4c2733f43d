import 'package:flutter/material.dart';
import 'package:nicexfer_app/core/theme/theme_helper.dart';

/// 统一的头部操作按钮组件
/// 整合了原有的 HeaderActionButton 和 HeaderActionButtonWidget 的功能
/// 支持多种样式和状态，提供更灵活的自定义选项
class UnifiedHeaderActionButton extends StatelessWidget {
  /// 按钮图标
  final IconData icon;

  /// 点击回调
  final VoidCallback? onPressed;

  /// 按钮大小
  final double size;

  /// 图标大小
  final double iconSize;

  /// 背景颜色（优先级最高）
  final Color? backgroundColor;

  /// 图标颜色（优先级最高）
  final Color? iconColor;

  /// 边框圆角
  final double borderRadius;

  /// 是否显示阴影
  final bool showShadow;

  /// 内边距
  final EdgeInsets? padding;

  /// 是否为激活状态
  final bool isActive;

  /// 是否为主要按钮
  final bool isPrimary;

  /// 是否启用动画效果
  final bool enableAnimation;

  /// 动画持续时间
  final Duration animationDuration;

  /// 是否显示边框
  final bool showBorder;

  /// 边框宽度
  final double borderWidth;

  /// 自定义边框颜色
  final Color? borderColor;

  /// 按钮样式类型
  final HeaderButtonStyle style;

  const UnifiedHeaderActionButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = 40,
    this.iconSize = 20,
    this.backgroundColor,
    this.iconColor,
    this.borderRadius = 12,
    this.showShadow = true,
    this.padding,
    this.isActive = false,
    this.isPrimary = false,
    this.enableAnimation = true,
    this.animationDuration = const Duration(milliseconds: 200),
    this.showBorder = false,
    this.borderWidth = 1.0,
    this.borderColor,
    this.style = HeaderButtonStyle.classic,
  });

  /// 创建经典样式的按钮（类似原 HeaderActionButton）
  const UnifiedHeaderActionButton.classic({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = 40,
    this.iconSize = 20,
    this.backgroundColor,
    this.iconColor,
    this.borderRadius = 12,
    this.showShadow = true,
    this.padding,
  })  : isActive = false,
        isPrimary = false,
        enableAnimation = false,
        animationDuration = const Duration(milliseconds: 200),
        showBorder = false,
        borderWidth = 1.0,
        borderColor = null,
        style = HeaderButtonStyle.classic;

  /// 创建现代样式的按钮（类似原 HeaderActionButtonWidget）
  const UnifiedHeaderActionButton.modern({
    super.key,
    required this.icon,
    this.onPressed,
    this.isActive = false,
    this.isPrimary = false,
    this.iconSize = 20,
  })  : size = 44,
        backgroundColor = null,
        iconColor = null,
        borderRadius = 12,
        showShadow = false,
        padding = null,
        enableAnimation = true,
        animationDuration = const Duration(milliseconds: 200),
        showBorder = true,
        borderWidth = 0.1,
        borderColor = null,
        style = HeaderButtonStyle.modern;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    // 根据样式和状态计算颜色
    final colors = _calculateColors(theme);

    final buttonContent = Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: BorderRadius.circular(borderRadius),
        border: showBorder
            ? Border.all(
                color: colors.border,
                width: borderWidth,
              )
            : null,
        boxShadow: _buildShadow(colors.shadow),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Padding(
            padding: padding ?? EdgeInsets.all((size - iconSize) / 2),
            child: Icon(
              icon,
              size: iconSize,
              color: colors.icon,
            ),
          ),
        ),
      ),
    );

    if (enableAnimation) {
      return AnimatedContainer(
        duration: animationDuration,
        curve: Curves.easeInOut,
        child: buttonContent,
      );
    }

    return buttonContent;
  }

  /// 计算按钮的各种颜色
  _ButtonColors _calculateColors(AppTheme theme) {
    Color bgColor;
    Color iColor;
    Color bColor;
    Color sColor;

    if (style == HeaderButtonStyle.modern) {
      // 现代样式的颜色计算
      if (isPrimary) {
        bgColor = backgroundColor ?? theme.primary.withValues(alpha: 0.15);
        iColor = iconColor ?? theme.primary;
        bColor = borderColor ?? theme.primary.withValues(alpha: 0.3);
        sColor = theme.primary.withValues(alpha: 0.15);
      } else if (isActive) {
        bgColor = backgroundColor ?? theme.secondary.withValues(alpha: 0.12);
        iColor = iconColor ?? theme.secondary;
        bColor = borderColor ?? theme.secondary.withValues(alpha: 0.25);
        sColor = theme.secondary.withValues(alpha: 0.15);
      } else {
        bgColor = backgroundColor ?? Colors.white.withValues(alpha: 0.08);
        iColor = iconColor ?? theme.onSurface.withValues(alpha: 0.8);
        bColor = borderColor ?? theme.onSurface.withValues(alpha: 0.1);
        sColor = Colors.transparent;
      }
    } else {
      // 经典样式的颜色计算
      bgColor = backgroundColor ??
          (theme.isDarkMode ? theme.surface.withValues(alpha: 0.8) : Colors.white.withValues(alpha: 0.9));
      iColor = iconColor ?? theme.onSurface;
      bColor = borderColor ?? theme.onSurface.withValues(alpha: 0.1);
      sColor = Colors.black.withValues(alpha: 0.1);
    }

    return _ButtonColors(
      background: bgColor,
      icon: iColor,
      border: bColor,
      shadow: sColor,
    );
  }

  /// 构建阴影效果
  List<BoxShadow>? _buildShadow(Color shadowColor) {
    if (!showShadow) return null;

    if (style == HeaderButtonStyle.modern && (isPrimary || isActive)) {
      return [
        BoxShadow(
          color: shadowColor,
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ];
    } else if (style == HeaderButtonStyle.classic) {
      return [
        BoxShadow(
          color: shadowColor,
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ];
    }

    return null;
  }
}

/// 按钮样式枚举
enum HeaderButtonStyle {
  /// 经典样式（类似原 HeaderActionButton）
  classic,

  /// 现代样式（类似原 HeaderActionButtonWidget）
  modern,
}

/// 按钮颜色配置
class _ButtonColors {
  final Color background;
  final Color icon;
  final Color border;
  final Color shadow;

  const _ButtonColors({
    required this.background,
    required this.icon,
    required this.border,
    required this.shadow,
  });
}
