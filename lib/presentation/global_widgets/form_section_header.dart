import 'package:flutter/material.dart';
import '../../core/theme/theme_helper.dart';

/// 表单节标题组件
/// 用于显示带图标的节标题和副标题
class FormSectionHeader extends StatelessWidget {
  /// 图标
  final IconData icon;

  /// 标题文本
  final String title;

  /// 副标题文本（可选）
  final String? subtitle;

  /// 图标颜色（可选，默认使用主题色）
  final Color? iconColor;

  /// 图标大小（默认24）
  final double iconSize;

  /// 标题和副标题之间的间距（默认8）
  final double spacing;

  const FormSectionHeader({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.iconColor,
    this.iconSize = 24,
    this.spacing = 8,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: iconColor ?? theme.primary,
              size: iconSize,
            ),
            SizedBox(width: AppTheme.spacingHorizontal12),
            Text(
              title,
              style: theme.formSectionTitleStyle,
            ),
          ],
        ),
        if (subtitle != null) ...[
          SizedBox(height: spacing),
          Text(
            subtitle!,
            style: theme.formSubtitleStyle,
          ),
        ],
      ],
    );
  }
}
