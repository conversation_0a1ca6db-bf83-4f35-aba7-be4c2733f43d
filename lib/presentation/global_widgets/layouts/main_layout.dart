import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:glass/glass.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:nicexfer_app/core/controllers/main_controller.dart';
import '../../../core/theme/theme_helper.dart';

import '../../../core/controllers/navigation_controller.dart';

class MainLayout extends GetView<MainController> {
  final Widget child;
  const MainLayout({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Scaffold(
      extendBody: true,
      extendBodyBehindAppBar: true,
      body: child,
      bottomNavigationBar: Builder(
        builder: (context) {
          return Container(
            decoration: BoxDecoration(
              color: theme.surface.withValues(alpha: 0.1),
            ),
            child: Safe<PERSON><PERSON>(
              child: Padding(
                padding: AppTheme.paddingSymmetricHorizontal15Vertical8,
                child: GetX<NavigationController>(
                  builder: (navigationController) {
                    return GNav(
                      // 交互效果颜色
                      rippleColor: theme.rippleColor, // 点击波纹颜色
                      hoverColor: theme.hoverColor, // 悬停颜色

                      // 标签样式
                      tabBorderRadius: AppTheme.borderRadiusValue12, // 标签圆角
                      tabBackgroundColor: theme.primary.withValues(alpha: 0.8), // 选中标签背景色

                      tabActiveBorder: Border.all(color: theme.primary.withValues(alpha: 0.8), width: 0.1),
                      // 颜色配置
                      activeColor: theme.primaryContainer, // 选中状态的图标和文字颜色
                      color: theme.primary, // 未选中状态的颜色

                      // 布局和动画
                      gap: AppTheme.spacingHorizontal8, // 图标和文字之间的间距
                      iconSize: 24, // 图标大小
                      padding: AppTheme.paddingSymmetricHorizontal15Vertical8, // 内边距
                      duration: const Duration(milliseconds: 300), // 动画持续时间，更流畅
                      curve: Curves.easeInOutCubic, // 动画曲线，更自然

                      // 交互设置
                      haptic: true, // 触觉反馈

                      // 导航配置
                      selectedIndex: navigationController.currentIndex,
                      onTabChange: navigationController.changeTabIndex,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,

                      // 标签列表
                      tabs: List.generate(
                        navigationController.labels.length,
                        (index) => GButton(
                          icon: _getIconData(
                            navigationController.icons[index],
                            isSelected: navigationController.currentIndex == index,
                          ),
                          text: navigationController.labels[index],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          );
        },
      ).asGlass(
        enabled: true,
        tintColor: Colors.transparent,
        blurX: 5,
        blurY: 5,
        frosted: false,
      ),
    );
  }

  /// 根据图标名称和选中状态获取对应的图标数据
  /// [iconName] 图标名称
  /// [isSelected] 是否为选中状态，默认为 false
  IconData _getIconData(String iconName, {bool isSelected = false}) {
    switch (iconName) {
      case 'dashboard':
        return isSelected ? LucideIcons.layoutDashboard : LucideIcons.layoutGrid;
      case 'send':
        return isSelected ? LucideIcons.waypoints : LucideIcons.send;
      case 'collect':
        return isSelected ? LucideIcons.boxes : LucideIcons.shapes;
      case 'history':
        return isSelected ? LucideIcons.logs : LucideIcons.library;
      case 'folder':
        return isSelected ? LucideIcons.folderOpen : LucideIcons.hardDrive;
      default:
        return isSelected ? LucideIcons.badgeHelp : LucideIcons.circleHelp;
    }
  }
}
