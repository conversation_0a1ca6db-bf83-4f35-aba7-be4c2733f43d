import 'package:flutter/material.dart';
import '../../../core/theme/theme_helper.dart';

/// 统一的筛选标签组件
/// 整合了项目中各种标签组件的设计模式，提供一致的视觉体验
class UnifiedFilterChip extends StatelessWidget {
  /// 标签文本
  final String label;

  /// 是否选中
  final bool isSelected;

  /// 点击回调
  final VoidCallback onTap;

  /// 图标（可选）
  final IconData? icon;

  /// 自定义颜色（可选，默认使用主题色）
  final Color? customColor;

  /// 标签样式
  final UnifiedFilterChipStyle style;

  /// 尺寸
  final UnifiedFilterChipSize size;

  /// 是否启用动画
  final bool enableAnimation;

  /// 动画时长
  final Duration animationDuration;

  /// 是否显示边框
  final bool showBorder;

  /// 自定义内边距
  final EdgeInsets? customPadding;

  /// 自定义圆角
  final double? customBorderRadius;

  const UnifiedFilterChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.icon,
    this.customColor,
    this.style = UnifiedFilterChipStyle.modern,
    this.size = UnifiedFilterChipSize.medium,
    this.enableAnimation = true,
    this.animationDuration = const Duration(milliseconds: 150),
    this.showBorder = true,
    this.customPadding,
    this.customBorderRadius,
  });

  /// 现代风格构造函数（默认）
  const UnifiedFilterChip.modern({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.icon,
    this.customColor,
    this.size = UnifiedFilterChipSize.medium,
    this.enableAnimation = true,
    this.animationDuration = const Duration(milliseconds: 150),
    this.showBorder = true,
    this.customPadding,
    this.customBorderRadius,
  }) : style = UnifiedFilterChipStyle.modern;

  /// 经典风格构造函数
  const UnifiedFilterChip.classic({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.icon,
    this.customColor,
    this.size = UnifiedFilterChipSize.medium,
    this.enableAnimation = true,
    this.animationDuration = const Duration(milliseconds: 200),
    this.showBorder = true,
    this.customPadding,
    this.customBorderRadius,
  }) : style = UnifiedFilterChipStyle.classic;

  /// 状态标签风格构造函数
  const UnifiedFilterChip.status({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.icon,
    this.customColor,
    this.size = UnifiedFilterChipSize.small,
    this.enableAnimation = false,
    this.animationDuration = const Duration(milliseconds: 150),
    this.showBorder = true,
    this.customPadding,
    this.customBorderRadius,
  }) : style = UnifiedFilterChipStyle.status;

  /// 紧凑风格构造函数
  const UnifiedFilterChip.compact({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.icon,
    this.customColor,
    this.size = UnifiedFilterChipSize.small,
    this.enableAnimation = true,
    this.animationDuration = const Duration(milliseconds: 120),
    this.showBorder = false,
    this.customPadding,
    this.customBorderRadius,
  }) : style = UnifiedFilterChipStyle.compact;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final chipConfig = _getChipConfig(theme);

    final chipBorderRadius = BorderRadius.circular(
      customBorderRadius ?? chipConfig.borderRadius,
    );

    final chipPadding = customPadding ?? chipConfig.padding;

    Widget chipContent = _buildChipContent(theme, chipConfig);

    if (enableAnimation) {
      chipContent = AnimatedContainer(
        duration: animationDuration,
        curve: chipConfig.animationCurve,
        padding: chipPadding,
        decoration: _buildDecoration(theme, chipConfig, chipBorderRadius),
        child: chipContent,
      );
    } else {
      chipContent = Container(
        padding: chipPadding,
        decoration: _buildDecoration(theme, chipConfig, chipBorderRadius),
        child: chipContent,
      );
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: chipBorderRadius,
        splashColor: chipConfig.splashColor,
        highlightColor: chipConfig.highlightColor,
        child: chipContent,
      ),
    );
  }

  /// 构建标签内容
  Widget _buildChipContent(AppTheme theme, _ChipConfig config) {
    final List<Widget> children = [];

    // 添加图标
    if (icon != null) {
      Widget iconWidget = Icon(
        icon!,
        size: config.iconSize,
        color: config.iconColor,
      );

      if (enableAnimation && style == UnifiedFilterChipStyle.modern) {
        iconWidget = AnimatedSwitcher(
          duration: animationDuration,
          transitionBuilder: (child, animation) {
            return ScaleTransition(
              scale: animation,
              child: child,
            );
          },
          child: iconWidget,
        );
      }

      children.add(iconWidget);

      if (label.isNotEmpty) {
        children.add(SizedBox(width: config.iconTextSpacing));
      }
    }

    // 添加文本
    if (label.isNotEmpty) {
      Widget textWidget = Text(label);

      if (enableAnimation && style == UnifiedFilterChipStyle.modern) {
        textWidget = AnimatedDefaultTextStyle(
          duration: animationDuration,
          curve: config.animationCurve,
          style: config.textStyle,
          child: textWidget,
        );
      } else {
        textWidget = DefaultTextStyle(
          style: config.textStyle,
          child: textWidget,
        );
      }

      children.add(textWidget);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  /// 构建装饰
  BoxDecoration _buildDecoration(
    AppTheme theme,
    _ChipConfig config,
    BorderRadius borderRadius,
  ) {
    return BoxDecoration(
      color: config.backgroundColor,
      borderRadius: borderRadius,
      border: showBorder && config.borderColor != null
          ? Border.all(
              color: config.borderColor!,
              width: config.borderWidth,
            )
          : null,
    );
  }

  /// 获取标签配置
  _ChipConfig _getChipConfig(AppTheme theme) {
    final effectiveColor = customColor ?? theme.primary;

    switch (style) {
      case UnifiedFilterChipStyle.modern:
        return _getModernConfig(theme, effectiveColor);
      case UnifiedFilterChipStyle.classic:
        return _getClassicConfig(theme, effectiveColor);
      case UnifiedFilterChipStyle.status:
        return _getStatusConfig(theme, effectiveColor);
      case UnifiedFilterChipStyle.compact:
        return _getCompactConfig(theme, effectiveColor);
    }
  }

  /// 现代风格配置
  _ChipConfig _getModernConfig(AppTheme theme, Color effectiveColor) {
    final sizeConfig = _getSizeConfig();

    return _ChipConfig(
      backgroundColor: isSelected ? effectiveColor : theme.surfaceContainerHighest.withValues(alpha: 0.5),
      borderColor: isSelected ? null : theme.outline.withValues(alpha: 0.3),
      borderWidth: 1.0,
      borderRadius: sizeConfig.borderRadius,
      padding: sizeConfig.padding,
      textStyle: theme.getTextStyle(
        fontSize: sizeConfig.fontSize,
        color: isSelected ? Colors.white : theme.onSurface.withValues(alpha: 0.85),
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
      ),
      iconColor: isSelected ? Colors.white : theme.onSurface.withValues(alpha: 0.7),
      iconSize: sizeConfig.iconSize,
      iconTextSpacing: 6.0,
      splashColor: effectiveColor.withValues(alpha: 0.12),
      highlightColor: effectiveColor.withValues(alpha: 0.08),
      animationCurve: Curves.fastOutSlowIn,
    );
  }

  /// 经典风格配置
  _ChipConfig _getClassicConfig(AppTheme theme, Color effectiveColor) {
    final sizeConfig = _getSizeConfig();

    return _ChipConfig(
      backgroundColor: isSelected ? effectiveColor : theme.onSurface.withValues(alpha: 0.05),
      borderColor: isSelected ? effectiveColor : theme.outline.withValues(alpha: 0.2),
      borderWidth: 1.0,
      borderRadius: sizeConfig.borderRadius,
      padding: sizeConfig.padding,
      textStyle: theme.getTextStyle(
        fontSize: sizeConfig.fontSize,
        color: isSelected ? Colors.white : theme.onSurface.withValues(alpha: 0.7),
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
      ),
      iconColor: isSelected ? Colors.white : theme.onSurface.withValues(alpha: 0.7),
      iconSize: sizeConfig.iconSize,
      iconTextSpacing: 8.0,
      splashColor: effectiveColor.withValues(alpha: 0.12),
      highlightColor: effectiveColor.withValues(alpha: 0.08),
      animationCurve: Curves.easeInOut,
    );
  }

  /// 状态标签风格配置
  _ChipConfig _getStatusConfig(AppTheme theme, Color effectiveColor) {
    final sizeConfig = _getSizeConfig();

    return _ChipConfig(
      backgroundColor: effectiveColor.withValues(alpha: 0.1),
      borderColor: effectiveColor.withValues(alpha: 0.4),
      borderWidth: 1.0,
      borderRadius: sizeConfig.borderRadius,
      padding: sizeConfig.padding,
      textStyle: theme.getTextStyle(
        fontSize: sizeConfig.fontSize,
        color: effectiveColor,
        fontWeight: FontWeight.w500,
      ),
      iconColor: effectiveColor,
      iconSize: sizeConfig.iconSize,
      iconTextSpacing: 8.0,
      splashColor: effectiveColor.withValues(alpha: 0.12),
      highlightColor: effectiveColor.withValues(alpha: 0.08),
      animationCurve: Curves.easeInOut,
    );
  }

  /// 紧凑风格配置
  _ChipConfig _getCompactConfig(AppTheme theme, Color effectiveColor) {
    final sizeConfig = _getSizeConfig();

    return _ChipConfig(
      backgroundColor:
          isSelected ? effectiveColor.withValues(alpha: 0.15) : theme.surfaceContainerHighest.withValues(alpha: 0.3),
      borderColor: null,
      borderWidth: 0.0,
      borderRadius: sizeConfig.borderRadius,
      padding: sizeConfig.padding,
      textStyle: theme.getTextStyle(
        fontSize: sizeConfig.fontSize,
        color: isSelected ? effectiveColor : theme.onSurface.withValues(alpha: 0.8),
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
      ),
      iconColor: isSelected ? effectiveColor : theme.onSurface.withValues(alpha: 0.7),
      iconSize: sizeConfig.iconSize,
      iconTextSpacing: 4.0,
      splashColor: effectiveColor.withValues(alpha: 0.12),
      highlightColor: effectiveColor.withValues(alpha: 0.08),
      animationCurve: Curves.fastOutSlowIn,
    );
  }

  /// 获取尺寸配置
  _SizeConfig _getSizeConfig() {
    switch (size) {
      case UnifiedFilterChipSize.small:
        return const _SizeConfig(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          fontSize: 12,
          iconSize: 14,
          borderRadius: 16,
        );
      case UnifiedFilterChipSize.medium:
        return const _SizeConfig(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          fontSize: 13,
          iconSize: 16,
          borderRadius: 20,
        );
      case UnifiedFilterChipSize.large:
        return const _SizeConfig(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          fontSize: 14,
          iconSize: 18,
          borderRadius: 24,
        );
    }
  }
}

/// 标签样式枚举
enum UnifiedFilterChipStyle {
  /// 现代风格 - 类似 RecordsFilterChips 的设计
  modern,

  /// 经典风格 - 类似 CategoryTab 的设计
  classic,

  /// 状态标签风格 - 类似 RecordCard 状态标签的设计
  status,

  /// 紧凑风格 - 适用于空间有限的场景
  compact,
}

/// 标签尺寸枚举
enum UnifiedFilterChipSize {
  small,
  medium,
  large,
}

/// 内部配置类
class _ChipConfig {
  final Color backgroundColor;
  final Color? borderColor;
  final double borderWidth;
  final double borderRadius;
  final EdgeInsets padding;
  final TextStyle textStyle;
  final Color iconColor;
  final double iconSize;
  final double iconTextSpacing;
  final Color splashColor;
  final Color highlightColor;
  final Curve animationCurve;

  const _ChipConfig({
    required this.backgroundColor,
    this.borderColor,
    required this.borderWidth,
    required this.borderRadius,
    required this.padding,
    required this.textStyle,
    required this.iconColor,
    required this.iconSize,
    required this.iconTextSpacing,
    required this.splashColor,
    required this.highlightColor,
    required this.animationCurve,
  });
}

/// 内部尺寸配置类
class _SizeConfig {
  final EdgeInsets padding;
  final double fontSize;
  final double iconSize;
  final double borderRadius;

  const _SizeConfig({
    required this.padding,
    required this.fontSize,
    required this.iconSize,
    required this.borderRadius,
  });
}
