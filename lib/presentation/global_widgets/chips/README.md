# 统一筛选标签组件 (UnifiedFilterChip)

## 概述

`UnifiedFilterChip` 是一个通用的筛选标签组件，整合了项目中各种标签组件的优秀设计模式，提供一致的视觉体验和交互效果。该组件支持多种样式、尺寸和自定义选项，适用于各种筛选和选择场景。

## 主要特性

- **多种样式**：现代风格、经典风格、状态标签风格、紧凑风格
- **灵活尺寸**：小、中、大三种预设尺寸
- **丰富动画**：支持平滑的选中状态切换动画
- **高度自定义**：支持自定义颜色、内边距、圆角等
- **响应式交互**：Material Design 风格的点击反馈
- **图标支持**：可选的图标显示，支持动画切换

## 基本用法

```dart
import '../../../global_widgets/chips/unified_filter_chip.dart';

// 基本用法
UnifiedFilterChip(
  label: '全部',
  isSelected: isSelected,
  onTap: () => onFilterChanged(),
  icon: Icons.dashboard,
)
```

## 样式变体

### 1. 现代风格 (Modern)

适用于现代化的界面设计，具有圆润的外观和平滑的动画效果。

```dart
UnifiedFilterChip.modern(
  label: '活跃',
  isSelected: selectedStatus == RecordStatus.active,
  onTap: () => filterByStatus(RecordStatus.active),
  icon: LucideIcons.circlePlay,
)
```

### 2. 经典风格 (Classic)

类似于 `CategoryTab` 的设计，适用于传统的标签页切换场景。

```dart
UnifiedFilterChip.classic(
  label: '文档',
  isSelected: selectedCategory == 'documents',
  onTap: () => selectCategory('documents'),
  size: UnifiedFilterChipSize.large,
)
```

### 3. 状态标签风格 (Status)

适用于显示状态信息，具有较为醒目的边框和颜色。

```dart
UnifiedFilterChip.status(
  label: '已完成',
  isSelected: true,
  onTap: () {},
  icon: Icons.check_circle,
  customColor: Colors.green,
)
```

### 4. 紧凑风格 (Compact)

适用于空间有限的场景，具有较小的内边距和更紧凑的布局。

```dart
UnifiedFilterChip.compact(
  label: '重要',
  isSelected: isImportant,
  onTap: () => toggleImportant(),
  size: UnifiedFilterChipSize.small,
)
```

## 尺寸选项

```dart
// 小尺寸
UnifiedFilterChip(
  label: '标签',
  isSelected: false,
  onTap: () {},
  size: UnifiedFilterChipSize.small,
)

// 中等尺寸（默认）
UnifiedFilterChip(
  label: '标签',
  isSelected: false,
  onTap: () {},
  size: UnifiedFilterChipSize.medium,
)

// 大尺寸
UnifiedFilterChip(
  label: '标签',
  isSelected: false,
  onTap: () {},
  size: UnifiedFilterChipSize.large,
)
```

## 高级自定义

```dart
UnifiedFilterChip(
  label: '自定义标签',
  isSelected: isSelected,
  onTap: () {},
  icon: Icons.star,
  customColor: Colors.purple,
  customPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
  customBorderRadius: 16.0,
  enableAnimation: true,
  animationDuration: Duration(milliseconds: 200),
  showBorder: true,
)
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `label` | `String` | 必需 | 标签文本 |
| `isSelected` | `bool` | 必需 | 是否选中 |
| `onTap` | `VoidCallback` | 必需 | 点击回调 |
| `icon` | `IconData?` | `null` | 图标（可选） |
| `customColor` | `Color?` | `null` | 自定义颜色 |
| `style` | `UnifiedFilterChipStyle` | `modern` | 标签样式 |
| `size` | `UnifiedFilterChipSize` | `medium` | 标签尺寸 |
| `enableAnimation` | `bool` | `true` | 是否启用动画 |
| `animationDuration` | `Duration` | `150ms` | 动画时长 |
| `showBorder` | `bool` | `true` | 是否显示边框 |
| `customPadding` | `EdgeInsets?` | `null` | 自定义内边距 |
| `customBorderRadius` | `double?` | `null` | 自定义圆角 |

## 迁移指南

### 从 RecordsFilterChips 迁移

**之前：**
```dart
class RecordsFilterChips extends GetWidget<RecordsController> {
  Widget _buildUnifiedChip(/* 参数 */) {
    // 复杂的实现逻辑
  }
}
```

**现在：**
```dart
class RecordsFilterChips extends GetWidget<RecordsController> {
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Row(
        children: [
          Obx(() => UnifiedFilterChip.modern(
            label: '全部',
            isSelected: controller.selectedStatus.value == null,
            onTap: () => controller.filterByStatus(null),
            icon: LucideIcons.layoutDashboard,
          )),
          // 其他标签...
        ],
      ),
    );
  }
}
```

### 从 CategoryTab 迁移

**之前：**
```dart
CategoryTab(
  text: '文档',
  isSelected: selectedCategory == 'documents',
  onTap: () => selectCategory('documents'),
  theme: theme,
)
```

**现在：**
```dart
UnifiedFilterChip.classic(
  label: '文档',
  isSelected: selectedCategory == 'documents',
  onTap: () => selectCategory('documents'),
)
```

### 从状态标签迁移

**之前：**
```dart
Container(
  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
  decoration: BoxDecoration(
    color: statusColor.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: statusColor.withValues(alpha: 0.4)),
  ),
  child: Row(
    children: [
      Icon(statusIcon, size: 16, color: statusColor),
      SizedBox(width: 8),
      Text(status.displayName),
    ],
  ),
)
```

**现在：**
```dart
UnifiedFilterChip.status(
  label: status.displayName,
  isSelected: true,
  onTap: () {},
  icon: statusIcon,
  customColor: statusColor,
)
```

## 设计原则

1. **一致性**：所有样式都遵循统一的设计语言和交互模式
2. **可访问性**：支持适当的点击区域和视觉反馈
3. **性能优化**：合理使用动画和重建优化
4. **主题适配**：自动适配应用主题色彩
5. **响应式设计**：支持不同屏幕尺寸和密度

## 注意事项

1. **动画性能**：在列表中使用大量标签时，考虑禁用动画以提升性能
2. **主题兼容**：确保自定义颜色与应用主题协调
3. **可访问性**：为标签提供适当的语义标识
4. **响应式布局**：在不同屏幕尺寸下测试标签的显示效果

## 示例场景

- **筛选器**：用于数据筛选和分类
- **标签页**：用于内容分类和导航
- **状态显示**：用于显示项目或任务状态
- **多选器**：用于多项选择场景
- **快速操作**：用于快速切换功能或模式