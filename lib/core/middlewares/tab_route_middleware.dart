import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/navigation_controller.dart';
import '../routes/app_routes.dart';

/// Tab 路由中间件
/// 拦截 Tab 页面的直接路由访问，重定向到主路由并设置对应的 Tab 索引
class TabRouteMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  GetPage? onPageCalled(GetPage? page) {
    if (page?.name != null) {
      // Tab 页面路由映射
      final Map<String, int> tabRouteMap = {
        AppRoutes.HOME: 0,
        AppRoutes.SEND: 1,
        AppRoutes.COLLECT: 2,
        AppRoutes.RECORDS: 3,
        AppRoutes.FILES: 4,
      };

      // 如果是 Tab 页面路由，设置对应的 Tab 索引
      if (tabRouteMap.containsKey(page!.name)) {
        final tabIndex = tabRouteMap[page.name]!;

        // 延迟设置 Tab 索引，确保 NavigationController 已初始化
        WidgetsBinding.instance.addPostFrameCallback((_) {
          try {
            final navigationController = Get.find<NavigationController>();
            navigationController.setTabIndex(tabIndex);
          } catch (e) {
            // 如果 NavigationController 还未初始化，忽略错误
            // 这种情况下会使用默认的 Tab 索引
          }
        });
      }
    }

    return page;
  }
}
