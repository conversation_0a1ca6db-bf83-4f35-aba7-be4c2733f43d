/// 应用常量配置
class AppConstants {
  // 应用信息
  static const String appName = 'NiceXfer';
  static const String appVersion = '1.0.0';
  static const String appDescription = '简单、安全、高效的文件传输应用';
  
  // API配置
  static const String baseUrl = 'https://api.nicexfer.com';
  static const String apiVersion = 'v1';
  static const Duration apiTimeout = Duration(seconds: 30);
  
  // Supabase配置（生产环境需要替换）
  static const String supabaseUrl = 'https://your-project.supabase.co';
  static const String supabaseAnonKey = 'your-anon-key';
  
  // 存储配置
  static const String storageBucket = 'files';
  static const int maxFileSize = 100 * 1024 * 1024; // 100MB
  static const int maxFilesPerTask = 50;
  static const List<String> allowedFileTypes = [
    'jpg', 'jpeg', 'png', 'gif', 'webp', // 图片
    'mp4', 'avi', 'mov', 'wmv', 'flv', // 视频
    'mp3', 'wav', 'aac', 'flac', 'ogg', // 音频
    'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', // 文档
    'txt', 'rtf', 'csv', // 文本
    'zip', 'rar', '7z', 'tar', 'gz', // 压缩包
  ];
  
  // 任务配置
  static const Duration defaultTaskExpiry = Duration(days: 7);
  static const Duration maxTaskExpiry = Duration(days: 30);
  static const Duration minTaskExpiry = Duration(hours: 1);
  static const int maxTasksPerUser = 100;
  
  // 用户配置
  static const int defaultStorageLimit = 1024 * 1024 * 1024; // 1GB
  static const int maxStorageLimit = 10 * 1024 * 1024 * 1024; // 10GB
  static const Duration sessionTimeout = Duration(hours: 24);
  
  // UI配置
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 2);
  static const double borderRadius = 12.0;
  static const double cardElevation = 2.0;
  
  // 分页配置
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // 缓存配置
  static const Duration cacheExpiry = Duration(hours: 1);
  static const int maxCacheSize = 50 * 1024 * 1024; // 50MB
  
  // 通知配置
  static const String notificationChannelId = 'nicexfer_notifications';
  static const String notificationChannelName = 'NiceXfer Notifications';
  static const String notificationChannelDescription = 'Notifications from NiceXfer app';
  
  // 错误消息
  static const String networkErrorMessage = '网络连接失败，请检查网络设置';
  static const String serverErrorMessage = '服务器错误，请稍后重试';
  static const String unknownErrorMessage = '未知错误，请稍后重试';
  static const String fileNotFoundMessage = '文件不存在或已被删除';
  static const String fileTooLargeMessage = '文件大小超过限制';
  static const String invalidFileTypeMessage = '不支持的文件类型';
  static const String taskExpiredMessage = '任务已过期';
  static const String taskNotFoundMessage = '任务不存在或已被删除';
  static const String unauthorizedMessage = '未授权访问，请重新登录';
  static const String forbiddenMessage = '权限不足，无法执行此操作';
  
  // 成功消息
  static const String taskCreatedMessage = '任务创建成功';
  static const String fileUploadedMessage = '文件上传成功';
  static const String fileDownloadedMessage = '文件下载成功';
  static const String taskCompletedMessage = '任务完成';
  static const String settingsSavedMessage = '设置已保存';
  
  // 正则表达式
  static const String emailRegex = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String passwordRegex = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
  static const String phoneRegex = r'^1[3-9]\d{9}$';
  
  // 日期格式
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm:ss';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'yyyy年MM月dd日';
  static const String displayTimeFormat = 'HH:mm';
  static const String displayDateTimeFormat = 'MM月dd日 HH:mm';
  
  // 本地存储键
  static const String keyAuthToken = 'auth_token';
  static const String keyUserData = 'user_data';
  static const String keyAppSettings = 'app_settings';
  static const String keyThemeMode = 'theme_mode';
  static const String keyLanguage = 'language';
  static const String keyFirstLaunch = 'first_launch';
  static const String keyLastSyncTime = 'last_sync_time';
  
  // 主题配置
  static const String lightTheme = 'light';
  static const String darkTheme = 'dark';
  static const String systemTheme = 'system';
  
  // 语言配置
  static const String languageZhCN = 'zh_CN';
  static const String languageEnUS = 'en_US';
  static const String languageJaJP = 'ja_JP';
  static const String languageKoKR = 'ko_KR';
  
  // 文件类型图标映射
  static const Map<String, String> fileTypeIcons = {
    // 图片
    'jpg': 'assets/icons/image.svg',
    'jpeg': 'assets/icons/image.svg',
    'png': 'assets/icons/image.svg',
    'gif': 'assets/icons/image.svg',
    'webp': 'assets/icons/image.svg',
    'bmp': 'assets/icons/image.svg',
    'svg': 'assets/icons/image.svg',
    
    // 视频
    'mp4': 'assets/icons/video.svg',
    'avi': 'assets/icons/video.svg',
    'mov': 'assets/icons/video.svg',
    'wmv': 'assets/icons/video.svg',
    'flv': 'assets/icons/video.svg',
    'mkv': 'assets/icons/video.svg',
    'webm': 'assets/icons/video.svg',
    
    // 音频
    'mp3': 'assets/icons/audio.svg',
    'wav': 'assets/icons/audio.svg',
    'aac': 'assets/icons/audio.svg',
    'flac': 'assets/icons/audio.svg',
    'ogg': 'assets/icons/audio.svg',
    'm4a': 'assets/icons/audio.svg',
    
    // 文档
    'pdf': 'assets/icons/pdf.svg',
    'doc': 'assets/icons/word.svg',
    'docx': 'assets/icons/word.svg',
    'xls': 'assets/icons/excel.svg',
    'xlsx': 'assets/icons/excel.svg',
    'ppt': 'assets/icons/powerpoint.svg',
    'pptx': 'assets/icons/powerpoint.svg',
    
    // 文本
    'txt': 'assets/icons/text.svg',
    'rtf': 'assets/icons/text.svg',
    'csv': 'assets/icons/text.svg',
    'json': 'assets/icons/code.svg',
    'xml': 'assets/icons/code.svg',
    'html': 'assets/icons/code.svg',
    'css': 'assets/icons/code.svg',
    'js': 'assets/icons/code.svg',
    'dart': 'assets/icons/code.svg',
    'java': 'assets/icons/code.svg',
    'py': 'assets/icons/code.svg',
    
    // 压缩包
    'zip': 'assets/icons/archive.svg',
    'rar': 'assets/icons/archive.svg',
    '7z': 'assets/icons/archive.svg',
    'tar': 'assets/icons/archive.svg',
    'gz': 'assets/icons/archive.svg',
    
    // 默认
    'default': 'assets/icons/file.svg',
  };
  
  // 文件大小单位
  static const List<String> fileSizeUnits = ['B', 'KB', 'MB', 'GB', 'TB'];
  
  // 任务状态颜色
  static const Map<String, int> taskStatusColors = {
    'pending': 0xFFFFA726, // 橙色
    'in_progress': 0xFF42A5F5, // 蓝色
    'completed': 0xFF66BB6A, // 绿色
    'expired': 0xFFEF5350, // 红色
    'cancelled': 0xFF9E9E9E, // 灰色
  };
  
  // 文件状态颜色
  static const Map<String, int> fileStatusColors = {
    'uploading': 0xFF42A5F5, // 蓝色
    'uploaded': 0xFF66BB6A, // 绿色
    'failed': 0xFFEF5350, // 红色
    'deleted': 0xFF9E9E9E, // 灰色
  };
  
  // 默认头像
  static const String defaultAvatarUrl = 'assets/images/default_avatar.png';
  
  // 应用图标
  static const String appIcon = 'assets/images/app_icon.png';
  static const String appLogo = 'assets/images/app_logo.png';
  
  // 空状态图片
  static const String emptyTasksImage = 'assets/images/empty_tasks.svg';
  static const String emptyFilesImage = 'assets/images/empty_files.svg';
  static const String emptyRecordsImage = 'assets/images/empty_records.svg';
  static const String noNetworkImage = 'assets/images/no_network.svg';
  static const String errorImage = 'assets/images/error.svg';
  
  // 社交链接
  static const String websiteUrl = 'https://nicexfer.com';
  static const String supportEmail = '<EMAIL>';
  static const String privacyPolicyUrl = 'https://nicexfer.com/privacy';
  static const String termsOfServiceUrl = 'https://nicexfer.com/terms';
  static const String githubUrl = 'https://github.com/nicexfer/nicexfer-app';
  
  // 应用商店链接
  static const String appStoreUrl = 'https://apps.apple.com/app/nicexfer';
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.nicexfer.app';
}