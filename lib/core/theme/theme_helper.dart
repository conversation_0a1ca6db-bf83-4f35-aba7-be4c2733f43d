import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_theme.dart';

/// 统一的应用主题管理类
/// 提供静态常量和主题数据访问的统一接口
class AppTheme {
  final ColorScheme colorScheme;
  final TextTheme textTheme;
  final bool isDarkMode;
  final Brightness brightness;

  const AppTheme._(
    this.colorScheme,
    this.textTheme,
    this.isDarkMode,
    this.brightness,
  );

  /// 从 BuildContext 创建主题数据
  factory AppTheme.of(BuildContext context) {
    final theme = Theme.of(context);
    return AppTheme._(
      theme.colorScheme,
      theme.textTheme,
      theme.brightness == Brightness.dark,
      theme.brightness,
    );
  }

  /// 创建应用主题
  /// [context] 构建上下文
  /// [bodyFontString] 正文字体名称，默认为 "Noto Sans SC"
  /// [displayFontString] 标题字体名称，默认为 "Noto Sans SC"
  static MaterialTheme createAppTheme(
    BuildContext context, {
    String bodyFontString = "Noto Sans SC",
    String displayFontString = "Noto Sans SC",
  }) {
    TextTheme baseTextTheme = Theme.of(context).textTheme;
    TextTheme bodyTextTheme = GoogleFonts.getTextTheme(bodyFontString, baseTextTheme);
    TextTheme displayTextTheme = GoogleFonts.getTextTheme(displayFontString, baseTextTheme);
    TextTheme textTheme = displayTextTheme.copyWith(
      bodyLarge: bodyTextTheme.bodyLarge,
      bodyMedium: bodyTextTheme.bodyMedium,
      bodySmall: bodyTextTheme.bodySmall,
      labelLarge: bodyTextTheme.labelLarge,
      labelMedium: bodyTextTheme.labelMedium,
      labelSmall: bodyTextTheme.labelSmall,
    );
    return MaterialTheme(textTheme);
  }

  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  // ==================== 静态常量 ====================

  /// 字体大小常量
  static double get fontSize12 => 12;
  static double get fontSize14 => 14;
  static double get fontSize16 => 16;
  static double get fontSize18 => 18;
  static double get fontSize20 => 20;
  static double get fontSize24 => 24;

  /// 字体粗细常量
  static const FontWeight fontWeightNormal = FontWeight.normal;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.bold;
  static const FontWeight fontWeightW700 = FontWeight.w700;

  /// 边距常量
  static double get padding8 => 8;
  static double get padding12 => 12;
  static double get padding16 => 16;
  static double get padding20 => 20;
  static double get padding24 => 24;

  /// 垂直间距常量
  static double get spacingVertical4 => 4;
  static double get spacingVertical8 => 8;
  static double get spacingVertical12 => 12;
  static double get spacingVertical16 => 16;
  static double get spacingVertical20 => 20;
  static double get spacingVertical24 => 24;
  static double get spacingVertical32 => 32;
  static double get spacingVertical64 => 64;

  /// 水平间距常量
  static double get spacingHorizontal4 => 4;
  static double get spacingHorizontal8 => 8;
  static double get spacingHorizontal12 => 12;
  static double get spacingHorizontal16 => 16;
  static double get spacingHorizontal20 => 20;
  static double get spacingHorizontal24 => 24;
  static double get spacingHorizontal32 => 32;

  /// 圆角常量
  static double get borderRadiusValue4 => 4;
  static double get borderRadiusValue8 => 8;
  static double get borderRadiusValue12 => 12;
  static double get borderRadiusValue16 => 16;

  /// 阴影高度常量
  static const double elevation0 = 0.0;

  /// 透明度常量
  static const double alpha08 = 0.08;
  static const double alpha1 = 0.1;
  static const double alpha12 = 0.12;
  static const double alpha3 = 0.3;
  static const double alpha8 = 0.8;
  static const double alpha150 = 150.0;

  /// EdgeInsets 常量
  static EdgeInsets get paddingAll8 => EdgeInsets.all(8);
  static EdgeInsets get paddingAll12 => EdgeInsets.all(12);
  static EdgeInsets get paddingAll16 => EdgeInsets.all(16);
  static EdgeInsets get paddingAll20 => EdgeInsets.all(20);
  static EdgeInsets get paddingAll24 => EdgeInsets.all(24);
  static const EdgeInsets paddingZero = EdgeInsets.zero;
  static EdgeInsets get paddingSymmetricVertical8 => EdgeInsets.symmetric(vertical: 8);
  static EdgeInsets get paddingSymmetricVertical12 => EdgeInsets.symmetric(vertical: 12);
  static EdgeInsets get paddingSymmetricVertical16 => EdgeInsets.symmetric(vertical: 16);
  static EdgeInsets get paddingSymmetricHorizontal15Vertical8 => EdgeInsets.symmetric(horizontal: 15, vertical: 8);
  static EdgeInsets get paddingSymmetricHorizontal20Vertical12 => EdgeInsets.symmetric(horizontal: 20, vertical: 12);
  static EdgeInsets get paddingOnlyBottom8 => EdgeInsets.only(bottom: 8);
  static EdgeInsets get paddingOnlyTop8 => EdgeInsets.only(top: 8);

  /// BorderRadius 常量
  static BorderRadius get borderRadius4 => BorderRadius.all(Radius.circular(4));
  static BorderRadius get borderRadius8 => BorderRadius.all(Radius.circular(8));
  static BorderRadius get borderRadius12 => BorderRadius.all(Radius.circular(12));
  static BorderRadius get borderRadius16 => BorderRadius.all(Radius.circular(16));

  // ==================== 实例方法 ====================

  /// 常用颜色的便捷访问
  Color get primary => colorScheme.primary;
  Color get primaryContainer => colorScheme.primaryContainer;
  Color get secondary => colorScheme.secondary;
  Color get secondaryContainer => colorScheme.secondaryContainer;
  Color get surface => colorScheme.surface;
  Color get surfaceContainer => colorScheme.surfaceContainer;
  Color get surfaceContainerHighest => colorScheme.surfaceContainerHighest;
  Color get onSurface => colorScheme.onSurface;
  Color get onSurfaceVariant => colorScheme.onSurfaceVariant;
  Color get onPrimary => colorScheme.onPrimary;
  Color get onSecondary => colorScheme.onSecondary;
  Color get outline => colorScheme.outline;
  Color get shadow => colorScheme.shadow;
  Color get error => colorScheme.error;
  Color get errorContainer => colorScheme.errorContainer;
  Color get tertiary => colorScheme.tertiary;
  Color get tertiaryContainer => colorScheme.tertiaryContainer;

  /// 根据主题模式获取适配颜色
  Color getAdaptiveColor({
    required Color lightColor,
    required Color darkColor,
  }) {
    return isDarkMode ? darkColor : lightColor;
  }

  /// 常用的主题适配颜色
  Color get adaptiveCardBackgroundColor => surfaceContainerHighest;
  Color get adaptiveBorderColor => outline;
  Color get adaptiveSurfaceColor => surfaceContainer;
  Color get adaptiveShadowColor => shadow.withValues(alpha: AppTheme.alpha1);

  /// 获取带透明度的颜色
  Color getColorWithAlpha(Color color, double alpha) {
    return color.withValues(alpha: alpha);
  }

  /// 获取涟漪效果颜色
  Color get rippleColor {
    return primary.withValues(
      alpha: isDarkMode ? AppTheme.alpha3 : AppTheme.alpha12,
    );
  }

  /// 获取悬停效果颜色
  Color get hoverColor {
    return primary.withValues(
      alpha: isDarkMode ? AppTheme.alpha12 : AppTheme.alpha08,
    );
  }

  /// 获取统计卡片颜色
  Color getStatCardColor(int index) {
    final colors = [
      primaryContainer,
      secondaryContainer,
      tertiaryContainer,
      errorContainer,
    ];
    return colors[index % colors.length];
  }

  /// 常用的 BoxDecoration
  BoxDecoration get cardDecoration {
    return BoxDecoration(
      color: adaptiveCardBackgroundColor,
      borderRadius: AppTheme.borderRadius12,
      border: Border.all(
        color: adaptiveBorderColor,
        width: 1.0,
      ),
      boxShadow: [
        BoxShadow(
          color: adaptiveShadowColor,
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  BoxDecoration get gradientDecoration {
    return BoxDecoration(
      gradient: LinearGradient(
        colors: [primary, primaryContainer],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: AppTheme.borderRadius16,
      boxShadow: [
        BoxShadow(
          color: primary.withValues(alpha: AppTheme.alpha3),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    );
  }

  BoxDecoration get fileItemDecoration {
    return BoxDecoration(
      color: adaptiveSurfaceColor,
      borderRadius: AppTheme.borderRadius8,
      border: Border.all(
        color: adaptiveBorderColor,
        width: 1.0,
      ),
    );
  }

  BoxDecoration getStatCardDecoration(Color color) {
    return BoxDecoration(
      color: getAdaptiveColor(
        lightColor: color.withValues(alpha: AppTheme.alpha1),
        darkColor: color.withValues(alpha: 0.15),
      ),
      borderRadius: AppTheme.borderRadius12,
      border: Border.all(
        color: color.withValues(alpha: AppTheme.alpha3),
        width: 1.0,
      ),
    );
  }

  /// 常用的按钮样式
  ButtonStyle get primaryButtonStyle {
    return ElevatedButton.styleFrom(
      padding: AppTheme.paddingSymmetricVertical16,
      backgroundColor: primary,
      foregroundColor: onPrimary,
      elevation: isDarkMode ? 2 : 1,
      shadowColor: adaptiveShadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: AppTheme.borderRadius8,
      ),
    );
  }

  ButtonStyle get secondaryButtonStyle {
    return ElevatedButton.styleFrom(
      padding: AppTheme.paddingSymmetricVertical16,
      backgroundColor: getAdaptiveColor(
        lightColor: secondary,
        darkColor: secondary.withValues(alpha: 0.8),
      ),
      foregroundColor: onSecondary,
      elevation: isDarkMode ? 2 : 1,
      shadowColor: adaptiveShadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: AppTheme.borderRadius8,
      ),
    );
  }

  ButtonStyle get outlinedButtonStyle {
    return OutlinedButton.styleFrom(
      padding: AppTheme.paddingSymmetricVertical16,
      foregroundColor: primary,
      side: BorderSide(
        color: getAdaptiveColor(
          lightColor: primary,
          darkColor: primary.withValues(alpha: 0.8),
        ),
        width: 1.0,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: AppTheme.borderRadius8,
      ),
    );
  }

  /// 预定义的文本样式
  TextStyle getTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color ?? onSurface,
    );
  }

  TextStyle get headlineStyle {
    return getTextStyle(
      fontSize: 24,
      fontWeight: AppTheme.fontWeightBold,
      color: onPrimary,
    );
  }

  TextStyle get subtitleStyle {
    return getTextStyle(
      fontSize: 16,
      fontWeight: AppTheme.fontWeightNormal,
      color: onPrimary.withValues(alpha: AppTheme.alpha8),
    );
  }

  TextStyle get sectionTitleStyle {
    return getTextStyle(
      fontSize: 20,
      fontWeight: AppTheme.fontWeightSemiBold,
      color: onSurface,
    );
  }

  TextStyle get cardTitleStyle {
    return getTextStyle(
      fontSize: 16,
      fontWeight: AppTheme.fontWeightSemiBold,
    );
  }

  TextStyle get cardSubtitleStyle {
    return getTextStyle(
      fontSize: 12,
      color: onSurfaceVariant,
    );
  }

  TextStyle get formLabelStyle {
    return getTextStyle(
      fontSize: 18,
      fontWeight: AppTheme.fontWeightSemiBold,
      color: onSurface,
    );
  }

  TextStyle get buttonTextStyle {
    return getTextStyle(
      fontSize: 16,
      fontWeight: AppTheme.fontWeightSemiBold,
    );
  }

  /// 获取SliverAppBar的系统UI样式
  SystemUiOverlayStyle get sliverAppBarSystemUIOverlayStyle {
    return isDarkMode ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark;
  }

  // ==================== 输入框样式 ====================

  /// 默认的 OutlineInputBorder 样式
  OutlineInputBorder get defaultOutlineInputBorder {
    return OutlineInputBorder(
      borderRadius: AppTheme.borderRadius12,
      borderSide: BorderSide(
        color: adaptiveBorderColor,
        width: 1.0,
      ),
    );
  }

  /// 聚焦状态的 OutlineInputBorder 样式
  OutlineInputBorder get focusedOutlineInputBorder {
    return OutlineInputBorder(
      borderRadius: AppTheme.borderRadius12,
      borderSide: BorderSide(
        color: primary,
        width: 2.0,
      ),
    );
  }

  /// 错误状态的 OutlineInputBorder 样式
  OutlineInputBorder get errorOutlineInputBorder {
    return OutlineInputBorder(
      borderRadius: AppTheme.borderRadius12,
      borderSide: BorderSide(
        color: error,
        width: 2.0,
      ),
    );
  }

  /// 禁用状态的 OutlineInputBorder 样式
  OutlineInputBorder get disabledOutlineInputBorder {
    return OutlineInputBorder(
      borderRadius: AppTheme.borderRadius12,
      borderSide: BorderSide(
        color: adaptiveBorderColor.withValues(alpha: 0.5),
        width: 1.0,
      ),
    );
  }

  /// 获取完整的 InputDecoration
  InputDecoration getInputDecoration({
    String? labelText,
    String? hintText,
    String? helperText,
    String? errorText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool enabled = true,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      errorText: errorText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      enabled: enabled,
      filled: true,
      fillColor: getAdaptiveColor(
        lightColor: surface,
        darkColor: surfaceContainer,
      ),
      labelStyle: getTextStyle(
        fontSize: AppTheme.fontSize14,
        color: enabled ? onSurfaceVariant : onSurfaceVariant.withValues(alpha: 0.5),
      ),
      hintStyle: getTextStyle(
        fontSize: AppTheme.fontSize14,
        color: onSurfaceVariant.withValues(alpha: 0.6),
      ),
      helperStyle: getTextStyle(
        fontSize: AppTheme.fontSize12,
        color: onSurfaceVariant,
      ),
      errorStyle: getTextStyle(
        fontSize: AppTheme.fontSize12,
        color: error,
      ),
      contentPadding: AppTheme.paddingSymmetricHorizontal20Vertical12,
      border: defaultOutlineInputBorder,
      enabledBorder: defaultOutlineInputBorder,
      focusedBorder: focusedOutlineInputBorder,
      errorBorder: errorOutlineInputBorder,
      focusedErrorBorder: errorOutlineInputBorder,
      disabledBorder: disabledOutlineInputBorder,
    );
  }

  /// 简化的输入框装饰器（只有边框）
  InputDecoration get simpleInputDecoration {
    return InputDecoration(
      border: defaultOutlineInputBorder,
      enabledBorder: defaultOutlineInputBorder,
      focusedBorder: focusedOutlineInputBorder,
      errorBorder: errorOutlineInputBorder,
      focusedErrorBorder: errorOutlineInputBorder,
      disabledBorder: disabledOutlineInputBorder,
      contentPadding: AppTheme.paddingSymmetricHorizontal20Vertical12,
    );
  }

  /// 搜索框样式的输入装饰器
  InputDecoration get searchInputDecoration {
    return InputDecoration(
      hintText: '搜索...',
      hintStyle: getTextStyle(
        fontSize: AppTheme.fontSize14,
        color: onSurfaceVariant.withValues(alpha: 0.6),
      ),
      prefixIcon: Icon(
        Icons.search,
        color: onSurfaceVariant,
        size: 20,
      ),
      filled: true,
      fillColor: surfaceContainer,
      contentPadding: AppTheme.paddingSymmetricHorizontal15Vertical8,
      border: OutlineInputBorder(
        borderRadius: AppTheme.borderRadius16,
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: AppTheme.borderRadius16,
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: AppTheme.borderRadius16,
        borderSide: BorderSide(
          color: primary,
          width: 1.0,
        ),
      ),
    );
  }

  // ==================== 专业表单样式 ====================

  /// 表单卡片装饰
  BoxDecoration get formCardDecoration {
    return BoxDecoration(
      color: getAdaptiveColor(
        lightColor: surface,
        darkColor: surfaceContainer,
      ),
      borderRadius: AppTheme.borderRadius16,
      border: Border.all(
        color: adaptiveBorderColor.withValues(alpha: 0.3),
        width: 1.0,
      ),
      boxShadow: [
        BoxShadow(
          color: adaptiveShadowColor.withValues(alpha: 0.08),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// 表单分组装饰
  BoxDecoration get formGroupDecoration {
    return BoxDecoration(
      color: getAdaptiveColor(
        lightColor: surface,
        darkColor: surfaceContainer,
      ),
      borderRadius: AppTheme.borderRadius12,
      border: Border.all(
        color: adaptiveBorderColor.withValues(alpha: 0.2),
        width: 1.0,
      ),
    );
  }

  /// 表单标题样式
  TextStyle get formSectionTitleStyle {
    return getTextStyle(
      fontSize: AppTheme.fontSize18,
      fontWeight: AppTheme.fontWeightSemiBold,
      color: onSurface,
    );
  }

  /// 表单子标题样式
  TextStyle get formSubtitleStyle {
    return getTextStyle(
      fontSize: AppTheme.fontSize14,
      color: onSurfaceVariant,
    );
  }

  /// 增强的输入框装饰器
  InputDecoration getEnhancedInputDecoration({
    String? labelText,
    String? hintText,
    String? helperText,
    String? errorText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool enabled = true,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      errorText: errorText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      enabled: enabled,
      filled: true,
      fillColor: getAdaptiveColor(
        lightColor: surface,
        darkColor: surfaceContainer,
      ),
      labelStyle: getTextStyle(
        fontSize: AppTheme.fontSize14,
        fontWeight: AppTheme.fontWeightMedium,
        color: enabled ? primary : onSurfaceVariant.withValues(alpha: 0.5),
      ),
      hintStyle: getTextStyle(
        fontSize: AppTheme.fontSize14,
        color: onSurfaceVariant.withValues(alpha: 0.6),
      ),
      helperStyle: getTextStyle(
        fontSize: AppTheme.fontSize12,
        color: onSurfaceVariant,
      ),
      errorStyle: getTextStyle(
        fontSize: AppTheme.fontSize12,
        color: error,
        fontWeight: AppTheme.fontWeightMedium,
      ),
      contentPadding: AppTheme.paddingSymmetricHorizontal20Vertical12,
      border: OutlineInputBorder(
        borderRadius: AppTheme.borderRadius12,
        borderSide: BorderSide(
          color: adaptiveBorderColor.withValues(alpha: 0.4),
          width: 1.5,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: AppTheme.borderRadius12,
        borderSide: BorderSide(
          color: adaptiveBorderColor.withValues(alpha: 0.4),
          width: 1.5,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: AppTheme.borderRadius12,
        borderSide: BorderSide(
          color: primary,
          width: 2.0,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: AppTheme.borderRadius12,
        borderSide: BorderSide(
          color: error,
          width: 2.0,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: AppTheme.borderRadius12,
        borderSide: BorderSide(
          color: error,
          width: 2.0,
        ),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: AppTheme.borderRadius12,
        borderSide: BorderSide(
          color: adaptiveBorderColor.withValues(alpha: 0.2),
          width: 1.0,
        ),
      ),
    );
  }

  /// 日期选择器装饰
  BoxDecoration get datePickerDecoration {
    return BoxDecoration(
      color: getAdaptiveColor(
        lightColor: surface,
        darkColor: surfaceContainer,
      ),
      borderRadius: AppTheme.borderRadius12,
      border: Border.all(
        color: adaptiveBorderColor.withValues(alpha: 0.4),
        width: 1.5,
      ),
    );
  }

  /// 增强的按钮样式
  ButtonStyle get enhancedPrimaryButtonStyle {
    return ElevatedButton.styleFrom(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      backgroundColor: primary,
      foregroundColor: onPrimary,
      elevation: isDarkMode ? 3 : 2,
      shadowColor: primary.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(
        borderRadius: AppTheme.borderRadius12,
      ),
      textStyle: getTextStyle(
        fontSize: AppTheme.fontSize16,
        fontWeight: AppTheme.fontWeightSemiBold,
      ),
    );
  }

  /// 增强的次要按钮样式
  ButtonStyle get enhancedSecondaryButtonStyle {
    return OutlinedButton.styleFrom(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      foregroundColor: primary,
      side: BorderSide(
        color: primary.withValues(alpha: 0.7),
        width: 1.5,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: AppTheme.borderRadius12,
      ),
      textStyle: getTextStyle(
        fontSize: AppTheme.fontSize16,
        fontWeight: AppTheme.fontWeightSemiBold,
      ),
    );
  }
}
