import "package:flutter/material.dart";

class MaterialTheme {
  final TextTheme textTheme;

  const MaterialTheme(this.textTheme);

  static ColorScheme lightScheme() {
    return const ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xff475d93),
      surfaceTint: Color(0xff475d93),
      onPrimary: Color(0xffffffff),
      primaryContainer: Color(0xffdae2ff), // 更浅的背景色，提高对比度
      onPrimaryContainer: Color(0xff001946), // 更深的文字色，提高对比度
      secondary: Color(0xff565e75),
      onSecondary: Color(0xffffffff),
      secondaryContainer: Color(0xffd7dffa),
      onSecondaryContainer: Color(0xff5a6279),
      tertiary: Color(0xff7d4d7e),
      onTertiary: Color(0xffffffff),
      tertiaryContainer: Color(0xffd49cd2),
      onTertiaryContainer: Color(0xff5e315f),
      error: Color(0xffba1a1a),
      onError: Color(0xffffffff),
      errorContainer: Color(0xffffdad6),
      onErrorContainer: Color(0xff93000a),
      surface: Color(0xfffaf8fe),
      onSurface: Color(0xff1b1b1f),
      onSurfaceVariant: Color(0xff44464f),
      outline: Color(0xff757780),
      outlineVariant: Color(0xffc5c6d1),
      shadow: Color(0xff000000),
      scrim: Color(0xff000000),
      inverseSurface: Color(0xff2f3034),
      inversePrimary: Color(0xffb1c5ff),
      primaryFixed: Color(0xffdae2ff),
      onPrimaryFixed: Color(0xff001946),
      primaryFixedDim: Color(0xffb1c5ff),
      onPrimaryFixedVariant: Color(0xff2e4579),
      secondaryFixed: Color(0xffdae2fd),
      onSecondaryFixed: Color(0xff131b2f),
      secondaryFixedDim: Color(0xffbec6e0),
      onSecondaryFixedVariant: Color(0xff3f465c),
      tertiaryFixed: Color(0xffffd6fb),
      onTertiaryFixed: Color(0xff320836),
      tertiaryFixedDim: Color(0xffeeb4eb),
      onTertiaryFixedVariant: Color(0xff633665),
      surfaceDim: Color(0xffdbd9de),
      surfaceBright: Color(0xfffaf8fe),
      surfaceContainerLowest: Color(0xffffffff),
      surfaceContainerLow: Color(0xfff4f3f8),
      surfaceContainer: Color(0xffefedf2),
      surfaceContainerHigh: Color(0xffe9e7ec),
      surfaceContainerHighest: Color(0xffe3e2e7),
    );
  }

  ThemeData light() {
    return theme(lightScheme());
  }

  static ColorScheme lightMediumContrastScheme() {
    return const ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xff1c3467),
      surfaceTint: Color(0xff475d93),
      onPrimary: Color(0xffffffff),
      primaryContainer: Color(0xff566ca2),
      onPrimaryContainer: Color(0xffffffff),
      secondary: Color(0xff2e364b),
      onSecondary: Color(0xffffffff),
      secondaryContainer: Color(0xff656d84),
      onSecondaryContainer: Color(0xffffffff),
      tertiary: Color(0xff512553),
      onTertiary: Color(0xffffffff),
      tertiaryContainer: Color(0xff8d5c8d),
      onTertiaryContainer: Color(0xffffffff),
      error: Color(0xff740006),
      onError: Color(0xffffffff),
      errorContainer: Color(0xffcf2c27),
      onErrorContainer: Color(0xffffffff),
      surface: Color(0xfffaf8fe),
      onSurface: Color(0xff101115),
      onSurfaceVariant: Color(0xff33363e),
      outline: Color(0xff50525b),
      outlineVariant: Color(0xff6b6d76),
      shadow: Color(0xff000000),
      scrim: Color(0xff000000),
      inverseSurface: Color(0xff2f3034),
      inversePrimary: Color(0xffb1c5ff),
      primaryFixed: Color(0xff566ca2),
      onPrimaryFixed: Color(0xffffffff),
      primaryFixedDim: Color(0xff3d5388),
      onPrimaryFixedVariant: Color(0xffffffff),
      secondaryFixed: Color(0xff656d84),
      onSecondaryFixed: Color(0xffffffff),
      secondaryFixedDim: Color(0xff4d546b),
      onSecondaryFixedVariant: Color(0xffffffff),
      tertiaryFixed: Color(0xff8d5c8d),
      onTertiaryFixed: Color(0xffffffff),
      tertiaryFixedDim: Color(0xff734474),
      onTertiaryFixedVariant: Color(0xffffffff),
      surfaceDim: Color(0xffc7c6cb),
      surfaceBright: Color(0xfffaf8fe),
      surfaceContainerLowest: Color(0xffffffff),
      surfaceContainerLow: Color(0xfff4f3f8),
      surfaceContainer: Color(0xffe9e7ec),
      surfaceContainerHigh: Color(0xffdddce1),
      surfaceContainerHighest: Color(0xffd2d1d6),
    );
  }

  ThemeData lightMediumContrast() {
    return theme(lightMediumContrastScheme());
  }

  static ColorScheme lightHighContrastScheme() {
    return const ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xff0f2a5d),
      surfaceTint: Color(0xff475d93),
      onPrimary: Color(0xffffffff),
      primaryContainer: Color(0xff31487c),
      onPrimaryContainer: Color(0xffffffff),
      secondary: Color(0xff242c40),
      onSecondary: Color(0xffffffff),
      secondaryContainer: Color(0xff41495f),
      onSecondaryContainer: Color(0xffffffff),
      tertiary: Color(0xff451b48),
      onTertiary: Color(0xffffffff),
      tertiaryContainer: Color(0xff663867),
      onTertiaryContainer: Color(0xffffffff),
      error: Color(0xff600004),
      onError: Color(0xffffffff),
      errorContainer: Color(0xff98000a),
      onErrorContainer: Color(0xffffffff),
      surface: Color(0xfffaf8fe),
      onSurface: Color(0xff000000),
      onSurfaceVariant: Color(0xff000000),
      outline: Color(0xff292c34),
      outlineVariant: Color(0xff474952),
      shadow: Color(0xff000000),
      scrim: Color(0xff000000),
      inverseSurface: Color(0xff2f3034),
      inversePrimary: Color(0xffb1c5ff),
      primaryFixed: Color(0xff31487c),
      onPrimaryFixed: Color(0xffffffff),
      primaryFixedDim: Color(0xff183064),
      onPrimaryFixedVariant: Color(0xffffffff),
      secondaryFixed: Color(0xff41495f),
      onSecondaryFixed: Color(0xffffffff),
      secondaryFixedDim: Color(0xff2a3247),
      onSecondaryFixedVariant: Color(0xffffffff),
      tertiaryFixed: Color(0xff663867),
      onTertiaryFixed: Color(0xffffffff),
      tertiaryFixedDim: Color(0xff4d224f),
      onTertiaryFixedVariant: Color(0xffffffff),
      surfaceDim: Color(0xffb9b8bd),
      surfaceBright: Color(0xfffaf8fe),
      surfaceContainerLowest: Color(0xffffffff),
      surfaceContainerLow: Color(0xfff2f0f5),
      surfaceContainer: Color(0xffe3e2e7),
      surfaceContainerHigh: Color(0xffd5d4d9),
      surfaceContainerHighest: Color(0xffc7c6cb),
    );
  }

  ThemeData lightHighContrast() {
    return theme(lightHighContrastScheme());
  }

  static ColorScheme darkScheme() {
    return const ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xffb5c9ff),
      surfaceTint: Color(0xffb1c5ff),
      onPrimary: Color(0xff152e61),
      primaryContainer: Color(0xff2e4579), // 更深的背景色，提高对比度
      onPrimaryContainer: Color(0xffdae2ff), // 更浅的文字色，提高对比度
      secondary: Color(0xffbec6e0),
      onSecondary: Color(0xff283045),
      secondaryContainer: Color(0xff3f465c),
      onSecondaryContainer: Color(0xffadb4ce),
      tertiary: Color(0xfff1b7ef),
      onTertiary: Color(0xff4a1f4d),
      tertiaryContainer: Color(0xffd49cd2),
      onTertiaryContainer: Color(0xff5e315f),
      error: Color(0xffffb4ab),
      onError: Color(0xff690005),
      errorContainer: Color(0xff93000a),
      onErrorContainer: Color(0xffffdad6),
      surface: Color(0xff121317),
      onSurface: Color(0xffe3e2e7),
      onSurfaceVariant: Color(0xffc5c6d1),
      outline: Color(0xff8e909a),
      outlineVariant: Color(0xff44464f),
      shadow: Color(0xff000000),
      scrim: Color(0xff000000),
      inverseSurface: Color(0xffe3e2e7),
      inversePrimary: Color(0xff475d93),
      primaryFixed: Color(0xffdae2ff),
      onPrimaryFixed: Color(0xff001946),
      primaryFixedDim: Color(0xffb1c5ff),
      onPrimaryFixedVariant: Color(0xff2e4579),
      secondaryFixed: Color(0xffdae2fd),
      onSecondaryFixed: Color(0xff131b2f),
      secondaryFixedDim: Color(0xffbec6e0),
      onSecondaryFixedVariant: Color(0xff3f465c),
      tertiaryFixed: Color(0xffffd6fb),
      onTertiaryFixed: Color(0xff320836),
      tertiaryFixedDim: Color(0xffeeb4eb),
      onTertiaryFixedVariant: Color(0xff633665),
      surfaceDim: Color(0xff121317),
      surfaceBright: Color(0xff38393d),
      surfaceContainerLowest: Color(0xff0d0e12),
      surfaceContainerLow: Color(0xff1b1b1f),
      surfaceContainer: Color(0xff1f1f23),
      surfaceContainerHigh: Color(0xff292a2e),
      surfaceContainerHighest: Color(0xff343439),
    );
  }

  ThemeData dark() {
    return theme(darkScheme());
  }

  static ColorScheme darkMediumContrastScheme() {
    return const ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xffd1dcff),
      surfaceTint: Color(0xffb1c5ff),
      onPrimary: Color(0xff062356),
      primaryContainer: Color(0xff3d5388), // 调整为更深的背景色
      onPrimaryContainer: Color(0xffdae2ff), // 调整为更浅的文字色
      secondary: Color(0xffd4dcf7),
      onSecondary: Color(0xff1d2539),
      secondaryContainer: Color(0xff8890a9),
      onSecondaryContainer: Color(0xff000000),
      tertiary: Color(0xffffcdfb),
      onTertiary: Color(0xff3e1441),
      tertiaryContainer: Color(0xffd49cd2),
      onTertiaryContainer: Color(0xff3b113f),
      error: Color(0xffffd2cc),
      onError: Color(0xff540003),
      errorContainer: Color(0xffff5449),
      onErrorContainer: Color(0xff000000),
      surface: Color(0xff121317),
      onSurface: Color(0xffffffff),
      onSurfaceVariant: Color(0xffdbdce7),
      outline: Color(0xffb0b1bc),
      outlineVariant: Color(0xff8e909a),
      shadow: Color(0xff000000),
      scrim: Color(0xff000000),
      inverseSurface: Color(0xffe3e2e7),
      inversePrimary: Color(0xff30467a),
      primaryFixed: Color(0xffdae2ff),
      onPrimaryFixed: Color(0xff000f31),
      primaryFixedDim: Color(0xffb1c5ff),
      onPrimaryFixedVariant: Color(0xff1c3467),
      secondaryFixed: Color(0xffdae2fd),
      onSecondaryFixed: Color(0xff081124),
      secondaryFixedDim: Color(0xffbec6e0),
      onSecondaryFixedVariant: Color(0xff2e364b),
      tertiaryFixed: Color(0xffffd6fb),
      onTertiaryFixed: Color(0xff25002a),
      tertiaryFixedDim: Color(0xffeeb4eb),
      onTertiaryFixedVariant: Color(0xff512553),
      surfaceDim: Color(0xff121317),
      surfaceBright: Color(0xff444448),
      surfaceContainerLowest: Color(0xff06070a),
      surfaceContainerLow: Color(0xff1d1d21),
      surfaceContainer: Color(0xff27282c),
      surfaceContainerHigh: Color(0xff323236),
      surfaceContainerHighest: Color(0xff3d3d41),
    );
  }

  ThemeData darkMediumContrast() {
    return theme(darkMediumContrastScheme());
  }

  static ColorScheme darkHighContrastScheme() {
    return const ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xffedefff),
      surfaceTint: Color(0xffb1c5ff),
      onPrimary: Color(0xff000000),
      primaryContainer: Color(0xffabc2fe),
      onPrimaryContainer: Color(0xff000a25),
      secondary: Color(0xffedefff),
      onSecondary: Color(0xff000000),
      secondaryContainer: Color(0xffbac2dc),
      onSecondaryContainer: Color(0xff030a1e),
      tertiary: Color(0xffffeafa),
      onTertiary: Color(0xff000000),
      tertiaryContainer: Color(0xffeab0e7),
      onTertiaryContainer: Color(0xff1b001f),
      error: Color(0xffffece9),
      onError: Color(0xff000000),
      errorContainer: Color(0xffffaea4),
      onErrorContainer: Color(0xff220001),
      surface: Color(0xff121317),
      onSurface: Color(0xffffffff),
      onSurfaceVariant: Color(0xffffffff),
      outline: Color(0xffeeeffa),
      outlineVariant: Color(0xffc1c2cd),
      shadow: Color(0xff000000),
      scrim: Color(0xff000000),
      inverseSurface: Color(0xffe3e2e7),
      inversePrimary: Color(0xff30467a),
      primaryFixed: Color(0xffdae2ff),
      onPrimaryFixed: Color(0xff000000),
      primaryFixedDim: Color(0xffb1c5ff),
      onPrimaryFixedVariant: Color(0xff000f31),
      secondaryFixed: Color(0xffdae2fd),
      onSecondaryFixed: Color(0xff000000),
      secondaryFixedDim: Color(0xffbec6e0),
      onSecondaryFixedVariant: Color(0xff081124),
      tertiaryFixed: Color(0xffffd6fb),
      onTertiaryFixed: Color(0xff000000),
      tertiaryFixedDim: Color(0xffeeb4eb),
      onTertiaryFixedVariant: Color(0xff25002a),
      surfaceDim: Color(0xff121317),
      surfaceBright: Color(0xff4f5054),
      surfaceContainerLowest: Color(0xff000000),
      surfaceContainerLow: Color(0xff1f1f23),
      surfaceContainer: Color(0xff2f3034),
      surfaceContainerHigh: Color(0xff3b3b3f),
      surfaceContainerHighest: Color(0xff46464b),
    );
  }

  ThemeData darkHighContrast() {
    return theme(darkHighContrastScheme());
  }

  ThemeData theme(ColorScheme colorScheme) => ThemeData(
        useMaterial3: true,
        brightness: colorScheme.brightness,
        colorScheme: colorScheme,
        textTheme: textTheme.apply(
          bodyColor: colorScheme.onSurface,
          displayColor: colorScheme.onSurface,
        ),
        scaffoldBackgroundColor: colorScheme.surface,
        canvasColor: colorScheme.surface,
      );

  List<ExtendedColor> get extendedColors => [];
}

class ExtendedColor {
  final Color seed, value;
  final ColorFamily light;
  final ColorFamily lightHighContrast;
  final ColorFamily lightMediumContrast;
  final ColorFamily dark;
  final ColorFamily darkHighContrast;
  final ColorFamily darkMediumContrast;

  const ExtendedColor({
    required this.seed,
    required this.value,
    required this.light,
    required this.lightHighContrast,
    required this.lightMediumContrast,
    required this.dark,
    required this.darkHighContrast,
    required this.darkMediumContrast,
  });
}

class ColorFamily {
  const ColorFamily({
    required this.color,
    required this.onColor,
    required this.colorContainer,
    required this.onColorContainer,
  });

  final Color color;
  final Color onColor;
  final Color colorContainer;
  final Color onColorContainer;
}
