import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// GetX 通用工具类
class GetXUtils {
  GetXUtils._();
  // 获取是否是横屏
  static bool get isLandscape => Get.mediaQuery.orientation == Orientation.landscape;

  /// 获取屏幕尺寸
  static Size get screenSize => Get.size;

  /// 获取屏幕宽度
  static double get screenWidth => Get.width;

  /// 获取屏幕高度
  static double get screenHeight => Get.height;

  /// 获取状态栏高度
  static double get statusBarHeight => Get.statusBarHeight;

  /// 获取底部安全区域高度
  static double get bottomBarHeight => Get.bottomBarHeight;

  /// 获取像素密度
  static double get pixelRatio => Get.pixelRatio;

  /// 检查是否为手机
  static bool get isPhone => GetPlatform.isMobile;

  /// 检查是否为平板
  static bool get isTablet => Get.width > 600;

  /// 检查是否为桌面
  static bool get isDesktop => GetPlatform.isDesktop;

  /// 检查是否为 Web
  static bool get isWeb => GetPlatform.isWeb;

  /// 检查是否为 Android
  static bool get isAndroid => GetPlatform.isAndroid;

  /// 检查是否为 iOS
  static bool get isIOS => GetPlatform.isIOS;

  /// 检查是否为暗色主题
  static bool get isDarkMode => Get.isDarkMode;

  /// 获取当前语言
  static Locale? get locale => Get.locale;

  /// 获取设备语言
  static Locale? get deviceLocale => Get.deviceLocale;

  /// 更改主题
  static void changeTheme(ThemeData theme) {
    Get.changeTheme(theme);
  }

  /// 切换主题模式
  static void changeThemeMode(ThemeMode themeMode) {
    Get.changeThemeMode(themeMode);
  }

  /// 更改语言
  static void changeLocale(Locale locale) {
    Get.updateLocale(locale);
  }

  /// 格式化文件大小
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// 格式化时间
  static String formatTime(DateTime dateTime, {String format = 'yyyy-MM-dd HH:mm:ss'}) {
    return dateTime.toString().substring(0, format.length);
  }

  /// 格式化相对时间
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 验证邮箱
  static bool isEmail(String email) {
    return GetUtils.isEmail(email);
  }

  /// 验证手机号
  static bool isPhoneNumber(String phoneNumber) {
    return GetUtils.isPhoneNumber(phoneNumber);
  }

  /// 验证 URL
  static bool isURL(String url) {
    return GetUtils.isURL(url);
  }

  /// 验证数字
  static bool isNumericOnly(String text) {
    return GetUtils.isNumericOnly(text);
  }

  /// 验证字母
  static bool isAlphabetOnly(String text) {
    return GetUtils.isAlphabetOnly(text);
  }

  /// 验证用户名
  static bool isUsername(String username) {
    return GetUtils.isUsername(username);
  }

  /// 验证密码强度
  static bool isStrongPassword(String password) {
    // 至少8位，包含大小写字母、数字和特殊字符
    final regex = RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$');
    return regex.hasMatch(password);
  }

  /// 验证身份证号
  static bool isIDCard(String idCard) {
    if (idCard.length != 18) return false;

    final regex = RegExp(r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$');
    return regex.hasMatch(idCard);
  }

  /// 生成随机字符串
  static String randomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return List.generate(length, (index) => chars[random % chars.length]).join();
  }

  /// 生成 UUID
  static String generateUUID() {
    final random = DateTime.now().millisecondsSinceEpoch;
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replaceAllMapped(
      RegExp(r'[xy]'),
      (match) {
        final r = (random + (random * 16)) % 16;
        final v = match.group(0) == 'x' ? r : (r & 0x3 | 0x8);
        return v.toRadixString(16);
      },
    );
  }

  /// JSON 编码
  static String jsonEncode(dynamic object) {
    return json.encode(object);
  }

  /// JSON 解码
  static dynamic jsonDecode(String source) {
    return json.decode(source);
  }

  /// 安全的 JSON 解码
  static T? safeJsonDecode<T>(String source, {T? defaultValue}) {
    try {
      final result = json.decode(source);
      return result is T ? result : defaultValue;
    } catch (e) {
      return defaultValue;
    }
  }

  /// 获取应用版本
  static String get appVersion {
    // 这里需要使用 package_info_plus 包来获取真实的应用版本
    return '1.0.0';
  }

  /// 获取构建号
  static String get buildNumber {
    // 这里需要使用 package_info_plus 包来获取真实的构建号
    return '1';
  }

  /// 检查网络连接
  static Future<bool> checkConnectivity() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  /// 打开 URL
  static Future<void> openURL(String url) async {
    // 这里需要使用 url_launcher 包来打开 URL
    debugPrint('打开 URL: $url');
  }

  /// 分享文本
  static Future<void> shareText(String text) async {
    // 这里需要使用 share_plus 包来分享文本
    debugPrint('分享文本: $text');
  }

  /// 分享文件
  static Future<void> shareFile(String filePath) async {
    // 这里需要使用 share_plus 包来分享文件
    debugPrint('分享文件: $filePath');
  }

  /// 保存图片到相册
  static Future<bool> saveImageToGallery(String imagePath) async {
    // 这里需要使用 image_gallery_saver 包来保存图片
    debugPrint('保存图片: $imagePath');
    return true;
  }

  /// 选择图片
  static Future<String?> pickImage() async {
    // 这里需要使用 image_picker 包来选择图片
    debugPrint('选择图片');
    return null;
  }

  /// 拍照
  static Future<String?> takePhoto() async {
    // 这里需要使用 image_picker 包来拍照
    debugPrint('拍照');
    return null;
  }

  /// 选择文件
  static Future<String?> pickFile() async {
    // 这里需要使用 file_picker 包来选择文件
    debugPrint('选择文件');
    return null;
  }

  /// 获取临时目录
  static Future<String> getTemporaryDirectory() async {
    // 这里需要使用 path_provider 包来获取临时目录
    return '/tmp';
  }

  /// 获取应用文档目录
  static Future<String> getApplicationDocumentsDirectory() async {
    // 这里需要使用 path_provider 包来获取应用文档目录
    return '/documents';
  }

  /// 获取应用支持目录
  static Future<String> getApplicationSupportDirectory() async {
    // 这里需要使用 path_provider 包来获取应用支持目录
    return '/support';
  }

  /// 请求权限
  static Future<bool> requestPermission(String permission) async {
    // 这里需要使用 permission_handler 包来请求权限
    debugPrint('请求权限: $permission');
    return true;
  }

  /// 检查权限
  static Future<bool> checkPermission(String permission) async {
    // 这里需要使用 permission_handler 包来检查权限
    debugPrint('检查权限: $permission');
    return true;
  }

  /// 打开应用设置
  static Future<void> openAppSettings() async {
    // 这里需要使用 permission_handler 包来打开应用设置
    debugPrint('打开应用设置');
  }
}

/// 日志工具类
class LogUtils {
  LogUtils._();

  static bool _isDebugMode = true;

  /// 设置调试模式
  static void setDebugMode(bool isDebug) {
    _isDebugMode = isDebug;
  }

  /// 调试日志
  static void debug(String message, [String? tag]) {
    if (_isDebugMode) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('🐛 DEBUG: $tagStr$message');
    }
  }

  /// 信息日志
  static void info(String message, [String? tag]) {
    if (_isDebugMode) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('ℹ️ INFO: $tagStr$message');
    }
  }

  /// 警告日志
  static void warning(String message, [String? tag]) {
    if (_isDebugMode) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('⚠️ WARNING: $tagStr$message');
    }
  }

  /// 错误日志
  static void error(String message, [String? tag, dynamic error, StackTrace? stackTrace]) {
    if (_isDebugMode) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('❌ ERROR: $tagStr$message');
      if (error != null) {
        debugPrint('Error details: $error');
      }
      if (stackTrace != null) {
        debugPrint('Stack trace: $stackTrace');
      }
    }
  }

  /// 网络日志
  static void network(String message, [String? tag]) {
    if (_isDebugMode) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('🌐 NETWORK: $tagStr$message');
    }
  }

  /// 性能日志
  static void performance(String message, [String? tag]) {
    if (_isDebugMode) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('⚡ PERFORMANCE: $tagStr$message');
    }
  }
}

/// 扩展方法
extension GetXStringExtensions on String {
  /// 是否为空或空白
  bool get isNullOrEmpty => isEmpty;

  /// 是否不为空
  bool get isNotNullOrEmpty => isNotEmpty;

  /// 首字母大写
  String get capitalize => GetUtils.capitalize(this) ?? this;

  /// 首字母小写
  String get capitalizeFirst => GetUtils.capitalizeFirst(this) ?? this;

  /// 移除所有空白字符
  String get removeAllWhitespace => GetUtils.removeAllWhitespace(this);

  /// 转换为驼峰命名
  String get camelCase => GetUtils.camelCase(this) ?? this;

  /// 转换为参数格式
  String get paramCase => GetUtils.paramCase(this) ?? this;

  /// 转换为数字
  int? get toInt => GetUtils.isNumericOnly(this) ? int.tryParse(this) : null;

  /// 转换为双精度浮点数
  double? get toDouble => double.tryParse(this);

  /// 转换为布尔值
  bool? get toBool {
    if (toLowerCase() == 'true') return true;
    if (toLowerCase() == 'false') return false;
    return null;
  }

  /// 验证邮箱
  bool get isEmail => GetUtils.isEmail(this);

  /// 验证手机号
  bool get isPhoneNumber => GetUtils.isPhoneNumber(this);

  /// 验证 URL
  bool get isURL => GetUtils.isURL(this);

  /// 验证数字
  bool get isNumericOnly => GetUtils.isNumericOnly(this);

  /// 验证字母
  bool get isAlphabetOnly => GetUtils.isAlphabetOnly(this);

  /// 验证用户名
  bool get isUsername => GetUtils.isUsername(this);
}

extension GetXListExtensions<T> on List<T> {
  /// 安全获取元素
  T? safeGet(int index) {
    if (index >= 0 && index < length) {
      return this[index];
    }
    return null;
  }

  /// 安全设置元素
  void safeSet(int index, T value) {
    if (index >= 0 && index < length) {
      this[index] = value;
    }
  }

  /// 添加元素（如果不存在）
  bool addIfNotExists(T item) {
    if (!contains(item)) {
      add(item);
      return true;
    }
    return false;
  }

  /// 移除多个元素
  void removeAll(List<T> items) {
    for (final item in items) {
      remove(item);
    }
  }

  /// 切换元素（存在则移除，不存在则添加）
  bool toggle(T item) {
    if (contains(item)) {
      remove(item);
      return false;
    } else {
      add(item);
      return true;
    }
  }

  /// 分块
  List<List<T>> chunk(int size) {
    final chunks = <List<T>>[];
    for (int i = 0; i < length; i += size) {
      chunks.add(sublist(i, (i + size > length) ? length : i + size));
    }
    return chunks;
  }

  /// 去重
  List<T> unique() {
    return toSet().toList();
  }

  /// 随机打乱
  List<T> shuffled() {
    final list = List<T>.from(this);
    list.shuffle();
    return list;
  }

  /// 随机获取一个元素
  T? random() {
    if (isEmpty) return null;
    final index = DateTime.now().millisecondsSinceEpoch % length;
    return this[index];
  }
}

extension GetXMapExtensions<K, V> on Map<K, V> {
  /// 安全获取值
  V? safeGet(K key) {
    return containsKey(key) ? this[key] : null;
  }

  /// 获取值或默认值
  V getOrDefault(K key, V defaultValue) {
    return containsKey(key) ? this[key]! : defaultValue;
  }

  /// 添加多个键值对
  void addAll(Map<K, V> other) {
    other.forEach((key, value) {
      this[key] = value;
    });
  }

  /// 移除多个键
  void removeKeys(List<K> keys) {
    for (final key in keys) {
      remove(key);
    }
  }

  /// 过滤
  Map<K, V> filter(bool Function(K key, V value) predicate) {
    final result = <K, V>{};
    forEach((key, value) {
      if (predicate(key, value)) {
        result[key] = value;
      }
    });
    return result;
  }

  /// 映射值
  Map<K, R> mapValues<R>(R Function(V value) mapper) {
    final result = <K, R>{};
    forEach((key, value) {
      result[key] = mapper(value);
    });
    return result;
  }

  /// 映射键
  Map<R, V> mapKeys<R>(R Function(K key) mapper) {
    final result = <R, V>{};
    forEach((key, value) {
      result[mapper(key)] = value;
    });
    return result;
  }
}

extension GetXDateTimeExtensions on DateTime {
  /// 格式化为字符串
  String format([String pattern = 'yyyy-MM-dd HH:mm:ss']) {
    return toString().substring(0, pattern.length);
  }

  /// 相对时间
  String get relativeTime => GetXUtils.formatRelativeTime(this);

  /// 是否为今天
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  /// 是否为昨天
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year && month == yesterday.month && day == yesterday.day;
  }

  /// 是否为明天
  bool get isTomorrow {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return year == tomorrow.year && month == tomorrow.month && day == tomorrow.day;
  }

  /// 是否为本周
  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return isAfter(startOfWeek) && isBefore(endOfWeek);
  }

  /// 是否为本月
  bool get isThisMonth {
    final now = DateTime.now();
    return year == now.year && month == now.month;
  }

  /// 是否为本年
  bool get isThisYear {
    final now = DateTime.now();
    return year == now.year;
  }

  /// 开始时间（当天 00:00:00）
  DateTime get startOfDay {
    return DateTime(year, month, day);
  }

  /// 结束时间（当天 23:59:59）
  DateTime get endOfDay {
    return DateTime(year, month, day, 23, 59, 59, 999);
  }

  /// 本周开始时间
  DateTime get startOfWeek {
    return subtract(Duration(days: weekday - 1)).startOfDay;
  }

  /// 本周结束时间
  DateTime get endOfWeek {
    return add(Duration(days: 7 - weekday)).endOfDay;
  }

  /// 本月开始时间
  DateTime get startOfMonth {
    return DateTime(year, month, 1);
  }

  /// 本月结束时间
  DateTime get endOfMonth {
    return DateTime(year, month + 1, 0, 23, 59, 59, 999);
  }

  /// 本年开始时间
  DateTime get startOfYear {
    return DateTime(year, 1, 1);
  }

  /// 本年结束时间
  DateTime get endOfYear {
    return DateTime(year, 12, 31, 23, 59, 59, 999);
  }
}
