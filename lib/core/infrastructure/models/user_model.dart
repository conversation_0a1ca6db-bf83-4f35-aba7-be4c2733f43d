enum UserRole { admin, user, guest }

enum UserStatus { active, inactive, suspended, pending }

class UserModel {
  final String id;
  final String email;
  final String? name;
  final String? avatar;
  final UserRole role;
  final UserStatus status;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final Map<String, dynamic>? preferences;
  final int storageUsed;
  final int storageLimit;
  final int tasksCreated;
  final int filesUploaded;

  UserModel({
    required this.id,
    required this.email,
    this.name,
    this.avatar,
    required this.role,
    required this.status,
    required this.createdAt,
    this.lastLoginAt,
    this.preferences,
    this.storageUsed = 0,
    this.storageLimit = 1073741824, // 1GB default
    this.tasksCreated = 0,
    this.filesUploaded = 0,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      email: json['email'],
      name: json['name'],
      avatar: json['avatar'],
      role: UserRole.values.byName(json['role']),
      status: UserStatus.values.byName(json['status']),
      createdAt: DateTime.parse(json['created_at']),
      lastLoginAt: json['last_login_at'] != null ? DateTime.parse(json['last_login_at']) : null,
      preferences: json['preferences'],
      storageUsed: json['storage_used'] ?? 0,
      storageLimit: json['storage_limit'] ?? 1073741824,
      tasksCreated: json['tasks_created'] ?? 0,
      filesUploaded: json['files_uploaded'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'avatar': avatar,
      'role': role.name,
      'status': status.name,
      'created_at': createdAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'preferences': preferences,
      'storage_used': storageUsed,
      'storage_limit': storageLimit,
      'tasks_created': tasksCreated,
      'files_uploaded': filesUploaded,
    };
  }

  // 获取显示名称
  String get displayName {
    return name?.isNotEmpty == true ? name! : email.split('@').first;
  }

  // 获取用户角色文本
  String get roleText {
    switch (role) {
      case UserRole.admin:
        return '管理员';
      case UserRole.user:
        return '用户';
      case UserRole.guest:
        return '访客';
    }
  }

  // 获取用户状态文本
  String get statusText {
    switch (status) {
      case UserStatus.active:
        return '活跃';
      case UserStatus.inactive:
        return '非活跃';
      case UserStatus.suspended:
        return '已暂停';
      case UserStatus.pending:
        return '待激活';
    }
  }

  // 获取存储使用率
  double get storageUsagePercentage {
    if (storageLimit == 0) return 0.0;
    return (storageUsed / storageLimit).clamp(0.0, 1.0);
  }

  // 获取存储使用量的可读格式
  String get storageUsedFormatted {
    return _formatBytes(storageUsed);
  }

  // 获取存储限制的可读格式
  String get storageLimitFormatted {
    return _formatBytes(storageLimit);
  }

  // 获取剩余存储空间
  int get storageRemaining {
    return (storageLimit - storageUsed).clamp(0, storageLimit);
  }

  // 获取剩余存储空间的可读格式
  String get storageRemainingFormatted {
    return _formatBytes(storageRemaining);
  }

  // 判断是否为管理员
  bool get isAdmin => role == UserRole.admin;

  // 判断是否为活跃用户
  bool get isActive => status == UserStatus.active;

  // 判断存储空间是否已满
  bool get isStorageFull => storageUsed >= storageLimit;

  // 判断存储空间是否接近满载（超过90%）
  bool get isStorageNearFull => storageUsagePercentage > 0.9;

  // 格式化字节数
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  // 获取用户偏好设置
  T? getPreference<T>(String key, [T? defaultValue]) {
    if (preferences == null) return defaultValue;
    return preferences![key] as T? ?? defaultValue;
  }

  // 创建副本并更新属性
  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? avatar,
    UserRole? role,
    UserStatus? status,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    Map<String, dynamic>? preferences,
    int? storageUsed,
    int? storageLimit,
    int? tasksCreated,
    int? filesUploaded,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
      storageUsed: storageUsed ?? this.storageUsed,
      storageLimit: storageLimit ?? this.storageLimit,
      tasksCreated: tasksCreated ?? this.tasksCreated,
      filesUploaded: filesUploaded ?? this.filesUploaded,
    );
  }
}
