import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

// 通知类型枚举
enum NotificationType {
  taskCreated,
  taskCompleted,
  taskExpired,
  fileUploaded,
  fileDownloaded,
  uploadProgress,
  downloadProgress,
  error,
  reminder,
}

// 通知数据模型
class NotificationData {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final Map<String, dynamic>? payload;
  final DateTime? scheduledTime;
  final bool silent;
  final String? iconPath;
  final String? soundPath;

  NotificationData({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    this.payload,
    this.scheduledTime,
    this.silent = false,
    this.iconPath,
    this.soundPath,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.name,
      'payload': payload,
      'scheduledTime': scheduledTime?.toIso8601String(),
      'silent': silent,
      'iconPath': iconPath,
      'soundPath': soundPath,
    };
  }

  factory NotificationData.fromJson(Map<String, dynamic> json) {
    return NotificationData(
      id: json['id'],
      title: json['title'],
      body: json['body'],
      type: NotificationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NotificationType.reminder,
      ),
      payload: json['payload'],
      scheduledTime: json['scheduledTime'] != null ? DateTime.parse(json['scheduledTime']) : null,
      silent: json['silent'] ?? false,
      iconPath: json['iconPath'],
      soundPath: json['soundPath'],
    );
  }
}

// 通知服务类
class NotificationService {
  static NotificationService? _instance;
  static FlutterLocalNotificationsPlugin? _notifications;

  NotificationService._();

  static Future<NotificationService> getInstance() async {
    _instance ??= NotificationService._();
    if (_notifications == null) {
      await _instance!._initialize();
    }
    return _instance!;
  }

  // 初始化通知服务
  Future<void> _initialize() async {
    _notifications = FlutterLocalNotificationsPlugin();

    // 初始化时区
    tz.initializeTimeZones();

    // Android初始化设置
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS初始化设置
    const DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    // 初始化设置
    const InitializationSettings initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
      macOS: iosSettings,
    );

    // 初始化插件
    await _notifications!.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // 请求权限
    await _requestPermissions();
  }

  // 请求通知权限
  Future<bool> _requestPermissions() async {
    // Android 13+ 需要请求通知权限
    final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
        _notifications!.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

    if (androidImplementation != null) {
      final bool? granted = await androidImplementation.requestNotificationsPermission();
      return granted ?? false;
    }

    // iOS权限请求
    final IOSFlutterLocalNotificationsPlugin? iosImplementation =
        _notifications!.resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>();

    if (iosImplementation != null) {
      final bool? granted = await iosImplementation.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      );
      return granted ?? false;
    }

    return true;
  }

  // 通知点击处理
  static void _onNotificationTapped(NotificationResponse response) {
    // TODO: 处理通知点击事件
    // 可以使用GetX路由导航到相应页面
    debugPrint('Notification tapped: ${response.payload}');
  }

  // 显示即时通知
  Future<void> showNotification(NotificationData data) async {
    final notificationDetails = _getNotificationDetails(data.type, data.silent);

    await _notifications!.show(
      data.id.hashCode,
      data.title,
      data.body,
      notificationDetails,
      payload: data.payload != null ? data.toJson().toString() : null,
    );
  }

  // 显示进度通知
  Future<void> showProgressNotification(
    String id,
    String title,
    String body,
    int progress,
    int maxProgress, {
    bool indeterminate = false,
  }) async {
    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'progress_channel',
      'Progress Notifications',
      channelDescription: 'Notifications for upload/download progress',
      importance: Importance.low,
      priority: Priority.low,
      showProgress: true,
      maxProgress: 100,
      progress: 0,
      indeterminate: false,
      ongoing: true,
      autoCancel: false,
    );

    final progressPercentage = ((progress / maxProgress) * 100).round();

    final updatedAndroidDetails = AndroidNotificationDetails(
      androidDetails.channelId,
      androidDetails.channelName,
      channelDescription: androidDetails.channelDescription,
      importance: androidDetails.importance,
      priority: androidDetails.priority,
      showProgress: true,
      maxProgress: 100,
      progress: progressPercentage,
      indeterminate: indeterminate,
      ongoing: true,
      autoCancel: false,
    );

    final NotificationDetails notificationDetails = NotificationDetails(
      android: updatedAndroidDetails,
    );

    await _notifications!.show(
      id.hashCode,
      title,
      '$body ($progressPercentage%)',
      notificationDetails,
    );
  }

  // 取消进度通知
  Future<void> cancelProgressNotification(String id) async {
    await _notifications!.cancel(id.hashCode);
  }

  // 调度通知
  Future<void> scheduleNotification(NotificationData data) async {
    if (data.scheduledTime == null) {
      throw ArgumentError('Scheduled time is required for scheduled notifications');
    }

    final notificationDetails = _getNotificationDetails(data.type, data.silent);

    await _notifications!.zonedSchedule(
      data.id.hashCode,
      data.title,
      data.body,
      tz.TZDateTime.from(data.scheduledTime!, tz.local),
      notificationDetails,
      payload: data.payload != null ? data.toJson().toString() : null,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    );
  }

  // 取消通知
  Future<void> cancelNotification(String id) async {
    await _notifications!.cancel(id.hashCode);
  }

  // 取消所有通知
  Future<void> cancelAllNotifications() async {
    await _notifications!.cancelAll();
  }

  // 获取待处理的通知
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications!.pendingNotificationRequests();
  }

  // 获取通知详情配置
  NotificationDetails _getNotificationDetails(NotificationType type, bool silent) {
    String channelId;
    String channelName;
    String channelDescription;
    Importance importance;
    Priority priority;

    switch (type) {
      case NotificationType.taskCreated:
        channelId = 'task_created';
        channelName = 'Task Created';
        channelDescription = 'Notifications when a new task is created';
        importance = Importance.defaultImportance;
        priority = Priority.defaultPriority;
        break;
      case NotificationType.taskCompleted:
        channelId = 'task_completed';
        channelName = 'Task Completed';
        channelDescription = 'Notifications when a task is completed';
        importance = Importance.high;
        priority = Priority.high;
        break;
      case NotificationType.taskExpired:
        channelId = 'task_expired';
        channelName = 'Task Expired';
        channelDescription = 'Notifications when a task expires';
        importance = Importance.high;
        priority = Priority.high;
        break;
      case NotificationType.fileUploaded:
        channelId = 'file_uploaded';
        channelName = 'File Uploaded';
        channelDescription = 'Notifications when a file is uploaded';
        importance = Importance.defaultImportance;
        priority = Priority.defaultPriority;
        break;
      case NotificationType.fileDownloaded:
        channelId = 'file_downloaded';
        channelName = 'File Downloaded';
        channelDescription = 'Notifications when a file is downloaded';
        importance = Importance.defaultImportance;
        priority = Priority.defaultPriority;
        break;
      case NotificationType.uploadProgress:
      case NotificationType.downloadProgress:
        channelId = 'progress';
        channelName = 'Progress';
        channelDescription = 'Progress notifications for uploads and downloads';
        importance = Importance.low;
        priority = Priority.low;
        break;
      case NotificationType.error:
        channelId = 'error';
        channelName = 'Errors';
        channelDescription = 'Error notifications';
        importance = Importance.high;
        priority = Priority.high;
        break;
      case NotificationType.reminder:
        channelId = 'reminder';
        channelName = 'Reminders';
        channelDescription = 'Reminder notifications';
        importance = Importance.defaultImportance;
        priority = Priority.defaultPriority;
        break;
    }

    final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: channelDescription,
      importance: silent ? Importance.low : importance,
      priority: silent ? Priority.low : priority,
      playSound: !silent,
      enableVibration: !silent,
      icon: '@mipmap/ic_launcher',
    );

    final DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: !silent,
    );

    return NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
      macOS: iosDetails,
    );
  }

  // 便捷方法：任务创建通知
  Future<void> notifyTaskCreated(String taskId, String taskTitle) async {
    final data = NotificationData(
      id: 'task_created_$taskId',
      title: '任务已创建',
      body: '任务「$taskTitle」已成功创建',
      type: NotificationType.taskCreated,
      payload: {'taskId': taskId, 'action': 'view_task'},
    );
    await showNotification(data);
  }

  // 便捷方法：任务完成通知
  Future<void> notifyTaskCompleted(String taskId, String taskTitle) async {
    final data = NotificationData(
      id: 'task_completed_$taskId',
      title: '任务已完成',
      body: '任务「$taskTitle」已完成',
      type: NotificationType.taskCompleted,
      payload: {'taskId': taskId, 'action': 'view_task'},
    );
    await showNotification(data);
  }

  // 便捷方法：任务过期通知
  Future<void> notifyTaskExpired(String taskId, String taskTitle) async {
    final data = NotificationData(
      id: 'task_expired_$taskId',
      title: '任务已过期',
      body: '任务「$taskTitle」已过期',
      type: NotificationType.taskExpired,
      payload: {'taskId': taskId, 'action': 'view_task'},
    );
    await showNotification(data);
  }

  // 便捷方法：文件上传完成通知
  Future<void> notifyFileUploaded(String fileName) async {
    final data = NotificationData(
      id: 'file_uploaded_${DateTime.now().millisecondsSinceEpoch}',
      title: '文件上传完成',
      body: '文件「$fileName」上传成功',
      type: NotificationType.fileUploaded,
    );
    await showNotification(data);
  }

  // 便捷方法：错误通知
  Future<void> notifyError(String title, String message) async {
    final data = NotificationData(
      id: 'error_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      body: message,
      type: NotificationType.error,
    );
    await showNotification(data);
  }

  // 便捷方法：提醒通知
  Future<void> scheduleReminder(
    String id,
    String title,
    String body,
    DateTime scheduledTime, {
    Map<String, dynamic>? payload,
  }) async {
    final data = NotificationData(
      id: 'reminder_$id',
      title: title,
      body: body,
      type: NotificationType.reminder,
      scheduledTime: scheduledTime,
      payload: payload,
    );
    await scheduleNotification(data);
  }
}
