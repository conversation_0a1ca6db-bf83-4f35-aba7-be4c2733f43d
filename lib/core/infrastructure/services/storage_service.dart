import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

// 存储服务类
class StorageService {
  static StorageService? _instance;
  static SharedPreferences? _preferences;

  StorageService._();

  static Future<StorageService> getInstance() async {
    _instance ??= StorageService._();
    _preferences ??= await SharedPreferences.getInstance();
    return _instance!;
  }

  // 存储键常量
  static const String _keyAuthToken = 'auth_token';
  static const String _keyUserData = 'user_data';
  static const String _keyAppSettings = 'app_settings';
  static const String _keyRecentTasks = 'recent_tasks';
  static const String _keyRecentFiles = 'recent_files';
  static const String _keyThemeMode = 'theme_mode';
  static const String _keyLanguage = 'language';
  static const String _keyNotificationSettings = 'notification_settings';
  static const String _keyUploadSettings = 'upload_settings';
  static const String _keyPrivacySettings = 'privacy_settings';

  // 基础存储方法
  Future<bool> setString(String key, String value) async {
    return await _preferences!.setString(key, value);
  }

  String? getString(String key) {
    return _preferences!.getString(key);
  }

  Future<bool> setInt(String key, int value) async {
    return await _preferences!.setInt(key, value);
  }

  int? getInt(String key) {
    return _preferences!.getInt(key);
  }

  Future<bool> setBool(String key, bool value) async {
    return await _preferences!.setBool(key, value);
  }

  bool? getBool(String key) {
    return _preferences!.getBool(key);
  }

  Future<bool> setDouble(String key, double value) async {
    return await _preferences!.setDouble(key, value);
  }

  double? getDouble(String key) {
    return _preferences!.getDouble(key);
  }

  Future<bool> setStringList(String key, List<String> value) async {
    return await _preferences!.setStringList(key, value);
  }

  List<String>? getStringList(String key) {
    return _preferences!.getStringList(key);
  }

  Future<bool> remove(String key) async {
    return await _preferences!.remove(key);
  }

  Future<bool> clear() async {
    return await _preferences!.clear();
  }

  bool containsKey(String key) {
    return _preferences!.containsKey(key);
  }

  Set<String> getKeys() {
    return _preferences!.getKeys();
  }

  // JSON对象存储
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    final jsonString = json.encode(value);
    return await setString(key, jsonString);
  }

  Map<String, dynamic>? getJson(String key) {
    final jsonString = getString(key);
    if (jsonString != null) {
      try {
        return json.decode(jsonString) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // JSON数组存储
  Future<bool> setJsonList(String key, List<Map<String, dynamic>> value) async {
    final jsonString = json.encode(value);
    return await setString(key, jsonString);
  }

  List<Map<String, dynamic>>? getJsonList(String key) {
    final jsonString = getString(key);
    if (jsonString != null) {
      try {
        final List<dynamic> decoded = json.decode(jsonString);
        return decoded.cast<Map<String, dynamic>>();
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // 认证相关
  Future<bool> setAuthToken(String token) async {
    return await setString(_keyAuthToken, token);
  }

  String? getAuthToken() {
    return getString(_keyAuthToken);
  }

  Future<bool> clearAuthToken() async {
    return await remove(_keyAuthToken);
  }

  bool isLoggedIn() {
    return getAuthToken() != null;
  }

  // 用户数据
  Future<bool> setUserData(Map<String, dynamic> userData) async {
    return await setJson(_keyUserData, userData);
  }

  Map<String, dynamic>? getUserData() {
    return getJson(_keyUserData);
  }

  Future<bool> clearUserData() async {
    return await remove(_keyUserData);
  }

  // 应用设置
  Future<bool> setAppSettings(Map<String, dynamic> settings) async {
    return await setJson(_keyAppSettings, settings);
  }

  Map<String, dynamic> getAppSettings() {
    return getJson(_keyAppSettings) ??
        {
          'theme_mode': 'system',
          'language': 'zh_CN',
          'auto_upload': true,
          'compress_images': true,
          'max_file_size': 100, // MB
          'notification_enabled': true,
          'sound_enabled': true,
          'vibration_enabled': true,
        };
  }

  // 主题模式
  Future<bool> setThemeMode(String mode) async {
    return await setString(_keyThemeMode, mode);
  }

  String getThemeMode() {
    return getString(_keyThemeMode) ?? 'system';
  }

  // 语言设置
  Future<bool> setLanguage(String language) async {
    return await setString(_keyLanguage, language);
  }

  String getLanguage() {
    return getString(_keyLanguage) ?? 'zh_CN';
  }

  // 最近任务
  Future<bool> setRecentTasks(List<Map<String, dynamic>> tasks) async {
    return await setJsonList(_keyRecentTasks, tasks);
  }

  List<Map<String, dynamic>> getRecentTasks() {
    return getJsonList(_keyRecentTasks) ?? [];
  }

  Future<bool> addRecentTask(Map<String, dynamic> task) async {
    final recentTasks = getRecentTasks();

    // 移除重复项
    recentTasks.removeWhere((t) => t['id'] == task['id']);

    // 添加到开头
    recentTasks.insert(0, task);

    // 限制数量
    if (recentTasks.length > 10) {
      recentTasks.removeRange(10, recentTasks.length);
    }

    return await setRecentTasks(recentTasks);
  }

  // 最近文件
  Future<bool> setRecentFiles(List<Map<String, dynamic>> files) async {
    return await setJsonList(_keyRecentFiles, files);
  }

  List<Map<String, dynamic>> getRecentFiles() {
    return getJsonList(_keyRecentFiles) ?? [];
  }

  Future<bool> addRecentFile(Map<String, dynamic> file) async {
    final recentFiles = getRecentFiles();

    // 移除重复项
    recentFiles.removeWhere((f) => f['id'] == file['id']);

    // 添加到开头
    recentFiles.insert(0, file);

    // 限制数量
    if (recentFiles.length > 20) {
      recentFiles.removeRange(20, recentFiles.length);
    }

    return await setRecentFiles(recentFiles);
  }

  // 通知设置
  Future<bool> setNotificationSettings(Map<String, dynamic> settings) async {
    return await setJson(_keyNotificationSettings, settings);
  }

  Map<String, dynamic> getNotificationSettings() {
    return getJson(_keyNotificationSettings) ??
        {
          'enabled': true,
          'sound': true,
          'vibration': true,
          'task_created': true,
          'task_completed': true,
          'file_uploaded': true,
          'file_downloaded': true,
          'task_expired': true,
        };
  }

  // 上传设置
  Future<bool> setUploadSettings(Map<String, dynamic> settings) async {
    return await setJson(_keyUploadSettings, settings);
  }

  Map<String, dynamic> getUploadSettings() {
    return getJson(_keyUploadSettings) ??
        {
          'auto_upload': true,
          'compress_images': true,
          'compress_quality': 80,
          'max_file_size': 100, // MB
          'allowed_types': ['image', 'document', 'video', 'audio'],
          'wifi_only': false,
        };
  }

  // 隐私设置
  Future<bool> setPrivacySettings(Map<String, dynamic> settings) async {
    return await setJson(_keyPrivacySettings, settings);
  }

  Map<String, dynamic> getPrivacySettings() {
    return getJson(_keyPrivacySettings) ??
        {
          'analytics_enabled': true,
          'crash_reports_enabled': true,
          'usage_data_enabled': true,
          'location_enabled': false,
          'auto_delete_expired': true,
          'auto_delete_days': 30,
        };
  }

  // 清除所有应用数据
  Future<bool> clearAllData() async {
    return await clear();
  }

  // 清除用户相关数据（保留应用设置）
  Future<bool> clearUserRelatedData() async {
    final futures = [
      clearAuthToken(),
      clearUserData(),
      remove(_keyRecentTasks),
      remove(_keyRecentFiles),
    ];

    final results = await Future.wait(futures);
    return results.every((result) => result);
  }

  // 导出设置
  Map<String, dynamic> exportSettings() {
    return {
      'app_settings': getAppSettings(),
      'notification_settings': getNotificationSettings(),
      'upload_settings': getUploadSettings(),
      'privacy_settings': getPrivacySettings(),
      'theme_mode': getThemeMode(),
      'language': getLanguage(),
    };
  }

  // 导入设置
  Future<bool> importSettings(Map<String, dynamic> settings) async {
    final futures = <Future<bool>>[];

    if (settings.containsKey('app_settings')) {
      futures.add(setAppSettings(settings['app_settings']));
    }

    if (settings.containsKey('notification_settings')) {
      futures.add(setNotificationSettings(settings['notification_settings']));
    }

    if (settings.containsKey('upload_settings')) {
      futures.add(setUploadSettings(settings['upload_settings']));
    }

    if (settings.containsKey('privacy_settings')) {
      futures.add(setPrivacySettings(settings['privacy_settings']));
    }

    if (settings.containsKey('theme_mode')) {
      futures.add(setThemeMode(settings['theme_mode']));
    }

    if (settings.containsKey('language')) {
      futures.add(setLanguage(settings['language']));
    }

    if (futures.isNotEmpty) {
      final results = await Future.wait(futures);
      return results.every((result) => result);
    }

    return true;
  }
}
