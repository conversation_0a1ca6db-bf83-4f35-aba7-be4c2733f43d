import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

// API响应模型
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final int? statusCode;
  final Map<String, dynamic>? errors;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.statusCode,
    this.errors,
  });

  factory ApiResponse.success(T data, {String? message, int? statusCode}) {
    return ApiResponse(
      success: true,
      data: data,
      message: message,
      statusCode: statusCode,
    );
  }

  factory ApiResponse.error(String message, {int? statusCode, Map<String, dynamic>? errors}) {
    return ApiResponse(
      success: false,
      message: message,
      statusCode: statusCode,
      errors: errors,
    );
  }
}

// API异常类
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? errors;

  ApiException(this.message, {this.statusCode, this.errors});

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode)';
  }
}

// 网络异常类
class NetworkException implements Exception {
  final String message;

  NetworkException(this.message);

  @override
  String toString() {
    return 'NetworkException: $message';
  }
}

// API服务类
class ApiService {
  static const String _baseUrl = 'https://api.nicexfer.com'; // TODO: 配置实际的API地址
  static const Duration _timeout = Duration(seconds: 30);

  final http.Client _client;
  String? _authToken;

  ApiService({http.Client? client}) : _client = client ?? http.Client();

  // 设置认证令牌
  void setAuthToken(String token) {
    _authToken = token;
  }

  // 清除认证令牌
  void clearAuthToken() {
    _authToken = null;
  }

  // 获取请求头
  Map<String, String> _getHeaders({Map<String, String>? additionalHeaders}) {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }

    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }

    return headers;
  }

  // 处理HTTP响应
  ApiResponse<T> _handleResponse<T>(http.Response response, T Function(Map<String, dynamic>) fromJson) {
    try {
      final Map<String, dynamic> responseData = json.decode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final data = fromJson(responseData);
        return ApiResponse.success(
          data,
          message: responseData['message'],
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          responseData['message'] ?? 'Request failed',
          statusCode: response.statusCode,
          errors: responseData['errors'],
        );
      }
    } catch (e) {
      return ApiResponse.error(
        'Failed to parse response: $e',
        statusCode: response.statusCode,
      );
    }
  }

  // 处理网络异常
  ApiResponse<T> _handleError<T>(dynamic error) {
    if (error is SocketException) {
      return ApiResponse.error('No internet connection');
    } else if (error is HttpException) {
      return ApiResponse.error('HTTP error: ${error.message}');
    } else if (error is FormatException) {
      return ApiResponse.error('Invalid response format');
    } else {
      return ApiResponse.error('Unexpected error: $error');
    }
  }

  // GET请求
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, String>? queryParameters,
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint').replace(queryParameters: queryParameters);

      final response = await _client.get(uri, headers: _getHeaders(additionalHeaders: headers)).timeout(_timeout);

      if (fromJson != null) {
        return _handleResponse(response, fromJson);
      } else {
        // 对于不需要解析的响应
        if (response.statusCode >= 200 && response.statusCode < 300) {
          return ApiResponse.success(null as T, statusCode: response.statusCode);
        } else {
          final responseData = json.decode(response.body);
          return ApiResponse.error(
            responseData['message'] ?? 'Request failed',
            statusCode: response.statusCode,
          );
        }
      }
    } catch (e) {
      return _handleError(e);
    }
  }

  // POST请求
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');

      final response = await _client
          .post(
            uri,
            headers: _getHeaders(additionalHeaders: headers),
            body: body != null ? json.encode(body) : null,
          )
          .timeout(_timeout);

      if (fromJson != null) {
        return _handleResponse(response, fromJson);
      } else {
        if (response.statusCode >= 200 && response.statusCode < 300) {
          return ApiResponse.success(null as T, statusCode: response.statusCode);
        } else {
          final responseData = json.decode(response.body);
          return ApiResponse.error(
            responseData['message'] ?? 'Request failed',
            statusCode: response.statusCode,
          );
        }
      }
    } catch (e) {
      return _handleError(e);
    }
  }

  // PUT请求
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');

      final response = await _client
          .put(
            uri,
            headers: _getHeaders(additionalHeaders: headers),
            body: body != null ? json.encode(body) : null,
          )
          .timeout(_timeout);

      if (fromJson != null) {
        return _handleResponse(response, fromJson);
      } else {
        if (response.statusCode >= 200 && response.statusCode < 300) {
          return ApiResponse.success(null as T, statusCode: response.statusCode);
        } else {
          final responseData = json.decode(response.body);
          return ApiResponse.error(
            responseData['message'] ?? 'Request failed',
            statusCode: response.statusCode,
          );
        }
      }
    } catch (e) {
      return _handleError(e);
    }
  }

  // DELETE请求
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');

      final response = await _client.delete(uri, headers: _getHeaders(additionalHeaders: headers)).timeout(_timeout);

      if (fromJson != null) {
        return _handleResponse(response, fromJson);
      } else {
        if (response.statusCode >= 200 && response.statusCode < 300) {
          return ApiResponse.success(null as T, statusCode: response.statusCode);
        } else {
          final responseData = json.decode(response.body);
          return ApiResponse.error(
            responseData['message'] ?? 'Request failed',
            statusCode: response.statusCode,
          );
        }
      }
    } catch (e) {
      return _handleError(e);
    }
  }

  // 文件上传
  Future<ApiResponse<T>> uploadFile<T>(
    String endpoint,
    String filePath,
    String fieldName, {
    Map<String, String>? fields,
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
    Function(double progress)? onProgress,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final request = http.MultipartRequest('POST', uri);

      // 添加请求头
      request.headers.addAll(_getHeaders(additionalHeaders: headers));

      // 添加文件
      final file = await http.MultipartFile.fromPath(fieldName, filePath);
      request.files.add(file);

      // 添加其他字段
      if (fields != null) {
        request.fields.addAll(fields);
      }

      // 发送请求
      final streamedResponse = await request.send().timeout(_timeout);
      final response = await http.Response.fromStream(streamedResponse);

      if (fromJson != null) {
        return _handleResponse(response, fromJson);
      } else {
        if (response.statusCode >= 200 && response.statusCode < 300) {
          return ApiResponse.success(null as T, statusCode: response.statusCode);
        } else {
          final responseData = json.decode(response.body);
          return ApiResponse.error(
            responseData['message'] ?? 'Upload failed',
            statusCode: response.statusCode,
          );
        }
      }
    } catch (e) {
      return _handleError(e);
    }
  }

  // 关闭客户端
  void dispose() {
    _client.close();
  }
}
