import 'package:get/get.dart';
import '../routes/app_routes.dart';

class NavigationController extends GetxController {
  static NavigationController get to => Get.find();
  // 当前选中的底部导航索引
  final _currentIndex = 0.obs;
  int get currentIndex => _currentIndex.value;

  // 底部导航页面路由列表
  final List<String> pages = [
    AppRoutes.HOME,
    AppRoutes.SEND,
    AppRoutes.COLLECT,
    AppRoutes.RECORDS,
    AppRoutes.FILES,
  ];

  // 底部导航标签
  final List<String> labels = [
    '仪表板',
    '发送',
    '收集',
    '记录',
    '文件',
  ];

  // 底部导航图标
  final List<String> icons = [
    'dashboard',
    'send',
    'collect',
    'history',
    'folder',
  ];

  @override
  void onInit() {
    super.onInit();
    // 根据当前路由设置初始索引
    final currentRoute = Get.currentRoute;
    updateIndexByRoute(currentRoute);
  }

  // 切换底部导航（混合模式：支持Tab切换和路由跳转）
  void changeTabIndex(int index) {
    if (index >= 0 && index < pages.length) {
      _currentIndex.value = index;
      // 可选：同时更新路由（用于深度链接支持）
      // Get.toNamed(pages[index]);
    }
  }

  // 通过路由跳转切换Tab
  void navigateToTab(int index) {
    if (index >= 0 && index < pages.length) {
      _currentIndex.value = index;
      Get.toNamed(pages[index]);
    }
  }

  // 根据路由更新索引
  void updateIndexByRoute(String route) {
    final index = pages.indexOf(route);
    if (index != -1) {
      _currentIndex.value = index;
    }
  }

  // 直接设置Tab索引（用于程序化导航和路由中间件）
  void setTabIndex(int index) {
    if (index >= 0 && index < pages.length) {
      _currentIndex.value = index;
    }
  }

  // 根据路由设置Tab索引（用于路由中间件）
  void setTabByRoute(String route) {
    final index = pages.indexOf(route);
    if (index != -1) {
      _currentIndex.value = index;
    }
  }
}
