import 'package:nicexfer_app/presentation/modules/collect/bindings/collect_binding.dart';
import 'package:nicexfer_app/presentation/modules/files/bindings/files_binding.dart';
import 'package:nicexfer_app/presentation/modules/home/<USER>/home_binding.dart';
import 'package:nicexfer_app/presentation/modules/records/bindings/records_binding.dart';
import 'package:nicexfer_app/presentation/modules/records/bindings/record_detail_binding.dart';
import 'package:nicexfer_app/presentation/modules/records/views/record_detail_page.dart';
import 'package:nicexfer_app/presentation/modules/send/bindings/send_binding.dart';
import 'package:nicexfer_app/presentation/modules/tasks/bindings/tasks_binding.dart';
import 'package:nicexfer_app/presentation/modules/tasks/views/tasks_page.dart';
import 'package:nicexfer_app/presentation/modules/main_tab/bindings/main_tab_binding.dart';
import 'package:nicexfer_app/presentation/modules/main_tab/views/main_tab_page.dart';
import 'package:nicexfer_app/core/middlewares/tab_route_middleware.dart';
import 'package:get/get.dart';
import 'package:nicexfer_app/presentation/modules/unknown_route/views/unknown_route_page.dart';
import 'app_routes.dart';

const _defaultTransition = Transition.native;

class AppPages {
  // Tab页面路由工厂方法
  static GetPage _createTabRoute(String routeName) {
    return GetPage(
      name: routeName,
      page: () => MainTabPage(),
      binding: _getBindingForRoute(routeName),
      middlewares: [TabRouteMiddleware()],
      transition: _defaultTransition,
    );
  }

  static final unknownRoutePage = GetPage(
    name: '/notfound',
    page: () => const UnknownRoutePage(),
    transition: _defaultTransition,
  );

  static final List<GetPage> pages = [
    unknownRoutePage,
    // 主容器路由
    GetPage(
      name: AppRoutes.MAIN,
      page: () => MainTabPage(),
      binding: MainTabBinding(),
      transition: _defaultTransition,
    ),
    // Tab页面路由 - 批量生成
    ..._tabRoutes.map(_createTabRoute),
    // 独立页面路由
    GetPage(
      name: AppRoutes.TASKS,
      page: () => const TasksPage(),
      binding: TasksBinding(),
      transition: _defaultTransition,
    ),
    // 记录详情页面
    GetPage(
      name: AppRoutes.RECORD_DETAIL,
      page: () => const RecordDetailPage(),
      binding: RecordDetailBinding(),
      transition: _defaultTransition,
    ),
    // 其他独立页面路由
    GetPage(
      name: AppRoutes.SETTINGS,
      page: () => const UnknownRoutePage(),
      transition: _defaultTransition,
    ),
  ];
  // Tab路由列表
  static const List<String> _tabRoutes = [
    AppRoutes.HOME,
    AppRoutes.SEND,
    AppRoutes.COLLECT,
    AppRoutes.RECORDS,
    AppRoutes.FILES,
  ];

  // 路由与 Binding 的映射
  static Bindings _getBindingForRoute(String routeName) {
    switch (routeName) {
      case AppRoutes.HOME:
        return HomeBinding();
      case AppRoutes.SEND:
        return SendBinding();
      case AppRoutes.COLLECT:
        return CollectBinding();
      case AppRoutes.RECORDS:
        return RecordsBinding();
      case AppRoutes.FILES:
        return FilesBinding();
      default:
        return MainTabBinding(); // 兜底方案
    }
  }
}
