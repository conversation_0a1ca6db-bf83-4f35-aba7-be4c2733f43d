import 'package:flutter/material.dart';
import 'package:nicexfer_app/core/di/dependency_injection.dart';

import 'app.dart';
import 'flavors.dart';

// 获取应用风味，默认为开发环境
String get appFlavor {
  return const String.fromEnvironment('FLAVOR', defaultValue: 'dev');
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await DependecyInjection.init();

  // 设置应用风味
  F.appFlavor = Flavor.values.firstWhere(
    (element) => element.name == appFlavor,
    orElse: () => Flavor.dev, // 如果没有找到匹配的风味，默认使用开发环境
  );

  runApp(const App());
}
