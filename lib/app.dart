import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nicexfer_app/core/theme/app_theme.dart';
import 'package:nicexfer_app/core/theme/theme_service.dart';
import 'package:nicexfer_app/core/translations/translations_service.dart';

import 'core/routes/app_pages.dart';
import 'core/routes/app_routes.dart';
import 'core/theme/theme_helper.dart';
import 'flavors.dart';

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    MaterialTheme theme = AppTheme.createAppTheme(context);
    return GetMaterialApp(
      title: F.title,
      debugShowCheckedModeBanner: false,
      theme: theme.light(),
      darkTheme: theme.dark(),
      themeMode: ThemeService.instance.themeMode,
      initialRoute: AppRoutes.MAIN,
      getPages: AppPages.pages,
      transitionDuration: const Duration(milliseconds: 300),
      translations: Translation(),
      locale: Get.locale,
      fallbackLocale: const Locale('en'),
      unknownRoute: AppPages.unknownRoutePage,
      builder: (context, child) {
        return _flavorBanner(child: child!);
      },
    );
  }

  Widget _flavorBanner({required Widget child, bool show = true}) => show
      ? Banner(
          location: BannerLocation.topStart,
          message: F.name,
          textStyle: TextStyle(
            fontWeight: AppTheme.fontWeightW700,
            fontSize: AppTheme.fontSize12,
            letterSpacing: 1.0,
          ),
          textDirection: TextDirection.ltr,
          child: child,
        )
      : Container(child: child);
}
