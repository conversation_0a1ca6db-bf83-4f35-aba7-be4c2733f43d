{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "dev Debug",
            "request": "launch",
            "type": "dart",
            "flutterMode": "debug",
            "args": [
                "--flavor",
                "dev"
            ],
            "program": "lib/main.dart"
        },
        {
            "name": "dev Profile",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "args": [
                "--flavor",
                "dev"
            ],
            "program": "lib/main.dart"
        },
        {
            "name": "dev Release",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--flavor",
                "dev"
            ],
            "program": "lib/main.dart"
        },
        {
            "name": "stg Debug",
            "request": "launch",
            "type": "dart",
            "flutterMode": "debug",
            "args": [
                "--flavor",
                "stg"
            ],
            "program": "lib/main.dart"
        },
        {
            "name": "stg Profile",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "args": [
                "--flavor",
                "stg"
            ],
            "program": "lib/main.dart"
        },
        {
            "name": "stg Release",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--flavor",
                "stg"
            ],
            "program": "lib/main.dart"
        },
        {
            "name": "prod Debug",
            "request": "launch",
            "type": "dart",
            "flutterMode": "debug",
            "args": [
                "--flavor",
                "prod"
            ],
            "program": "lib/main.dart"
        },
        {
            "name": "prod Profile",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "args": [
                "--flavor",
                "prod"
            ],
            "program": "lib/main.dart"
        },
        {
            "name": "prod Release",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--flavor",
                "prod"
            ],
            "program": "lib/main.dart"
        },
        {
            "name": "Flutter for web (hot reloadable)",
            "type": "dart",
            "request": "launch",
            "program": "lib/main.dart",
            "args": [
                "-d",
                "chrome",
                "--web-experimental-hot-reload",
            ]
        }
    ]
}