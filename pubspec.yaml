name: nicexfer_app
description: A new Flutter project.

version: 1.0.0+1

environment:
  sdk: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.8
  get: ^4.7.2
  get_storage: ^2.1.1
  flutter_lints: ^6.0.0
  freezed_annotation: ^3.0.0
  dartz: ^0.10.1
  json_serializable: ^6.9.5
  rxdart: ^0.28.0
  shared_preferences: ^2.5.3
  flutter_local_notifications: ^19.2.1
  timezone: ^0.10.1
  google_nav_bar: ^5.0.7
  google_fonts: ^6.2.1
  dotted_border: ^3.0.1
  flutter_tilt: ^3.2.1
  lucide_icons_flutter: ^3.0.5
  glass: ^2.0.0+2

  http: any
  intl: ^0.20.2
dev_dependencies:
  flutter_flavorizr: ^2.4.1
  build_runner: ^2.4.14
  freezed: ^3.0.6

  # #to make native splash screen
  # # 1) change path image_path
  # # 2) run commened -> flutter pub run flutter_native_splash:create
  #   flutter_native_splash: ^2.1.1
  # flutter_native_splash:
  #   background_image: assets/image/splash_background.png # choose one color or background
  #   color: "#ffffff"
  #   image: assets/icons/icon.png

  # # to change icon
  # # 1) change path image_path
  # # 2) run commened -> flutter pub run flutter_launcher_icons:main
  #   flutter_launcher_icons: ^0.9.2
  # flutter_icons:
  #   android: true
  #   ios: true
  #   remove_alpha_ios: true
  #   image_path: "assets/icons/icon.png"

  flutter_test:
    sdk: flutter

flutter:
  assets:
    - assets/icons/
    - assets/image/
    - assets/animation/
    - assets/mock_files_data.json
  uses-material-design: true
