{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-28T05:38:03.472Z", "updatedAt": "2025-06-28T05:38:03.475Z", "resourceCount": 4}, "resources": [{"id": "flutter-expert", "source": "project", "protocol": "role", "name": "Flutter Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/flutter-expert/flutter-expert.role.md", "metadata": {"createdAt": "2025-06-28T05:38:03.474Z", "updatedAt": "2025-06-28T05:38:03.474Z", "scannedAt": "2025-06-28T05:38:03.474Z"}}, {"id": "flutter-memory", "source": "project", "protocol": "thought", "name": "Flutter Memory 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/flutter-expert/thought/flutter-memory.thought.md", "metadata": {"createdAt": "2025-06-28T05:38:03.475Z", "updatedAt": "2025-06-28T05:38:03.475Z", "scannedAt": "2025-06-28T05:38:03.475Z"}}, {"id": "flutter-recall", "source": "project", "protocol": "thought", "name": "Flutter Recall 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/flutter-expert/thought/flutter-recall.thought.md", "metadata": {"createdAt": "2025-06-28T05:38:03.475Z", "updatedAt": "2025-06-28T05:38:03.475Z", "scannedAt": "2025-06-28T05:38:03.475Z"}}, {"id": "flutter-workflow", "source": "project", "protocol": "execution", "name": "Flutter Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/flutter-expert/execution/flutter-workflow.execution.md", "metadata": {"createdAt": "2025-06-28T05:38:03.475Z", "updatedAt": "2025-06-28T05:38:03.475Z", "scannedAt": "2025-06-28T05:38:03.475Z"}}], "stats": {"totalResources": 4, "byProtocol": {"role": 1, "thought": 2, "execution": 1}, "bySource": {"project": 4}}}