<reference protocol="execution" resource="flutter-workflow">
<constraint>
## Flutter开发约束
- **框架版本**：确保使用稳定版本的Flutter SDK
- **Dart版本**：遵循Flutter要求的Dart版本兼容性
- **平台限制**：考虑iOS、Android平台的特定约束
- **性能要求**：保持60fps的流畅体验
- **内存限制**：合理控制应用内存占用
- **包大小**：控制APK/IPA文件大小
</constraint>

<rule>
## Flutter开发强制规则
- **代码规范**：严格遵循Dart和Flutter官方代码风格
- **状态管理**：统一使用GetX进行状态管理和依赖注入
- **文件组织**：按功能模块组织代码结构
- **命名约定**：使用驼峰命名法，类名首字母大写
- **导入顺序**：Dart库 → Flutter库 → 第三方库 → 项目内部库
- **错误处理**：必须处理所有可能的异常情况
- **空安全**：充分利用Dart的空安全特性
</rule>

<guideline>
## Flutter开发指导原则
- **组件化思维**：构建可复用的Widget组件
- **性能优先**：避免不必要的重建和内存泄漏
- **用户体验**：注重交互反馈和加载状态
- **响应式设计**：适配不同屏幕尺寸和方向
- **渐进增强**：优先实现核心功能，再添加高级特性
- **测试驱动**：编写单元测试和Widget测试
</guideline>

<process>
## Flutter开发完整流程

### 阶段一：项目分析与规划
```mermaid
flowchart TD
    A[需求分析] --> B[技术选型]
    B --> C[架构设计]
    C --> D[UI/UX设计]
    D --> E[开发计划]
    
    B1[Flutter版本] --> B
    B2[状态管理方案] --> B
    B3[第三方库选择] --> B
    
    C1[目录结构] --> C
    C2[模块划分] --> C
    C3[数据流设计] --> C
```

#### 1.1 需求分析
- **功能需求**：明确应用的核心功能和用户场景
- **性能需求**：确定性能指标和优化目标
- **平台需求**：确定目标平台（iOS/Android/Web/Desktop）
- **兼容性需求**：确定支持的设备和系统版本

#### 1.2 技术选型
- **Flutter版本**：选择稳定版本，考虑新特性需求
- **状态管理**：推荐使用GetX，考虑项目复杂度
- **UI框架**：Material Design 3 或 Cupertino
- **第三方库**：网络请求、数据存储、图片处理等

#### 1.3 架构设计
```
lib/
├── app.dart                 # 应用入口配置
├── main.dart               # 主函数
├── core/                   # 核心功能
│   ├── constants/          # 常量定义
│   ├── controllers/        # 全局控制器
│   ├── di/                # 依赖注入
│   ├── routes/            # 路由配置
│   ├── theme/             # 主题配置
│   └── utils/             # 工具函数
└── presentation/          # 表现层
    ├── modules/           # 功能模块
    │   └── feature/       # 具体功能
    │       ├── bindings/  # 依赖绑定
    │       ├── controllers/ # 控制器
    │       ├── models/    # 数据模型
    │       ├── repositories/ # 数据仓库
    │       ├── views/     # 视图页面
    │       └── widgets/   # 自定义组件
    └── shared/            # 共享组件
```

### 阶段二：开发实现

#### 2.1 环境配置
```bash
# 检查Flutter环境
flutter doctor

# 创建项目
flutter create --org com.example project_name

# 添加依赖
flutter pub add get
flutter pub add get_storage
```

#### 2.2 基础架构搭建
```dart
// main.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'app.dart';

void main() {
  runApp(MyApp());
}

// app.dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Flutter App',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
      ),
      initialRoute: '/',
      getPages: AppRoutes.routes,
    );
  }
}
```

#### 2.3 GetX架构实现
```dart
// Controller示例
class HomeController extends GetxController {
  final _isLoading = false.obs;
  final _data = <String>[].obs;
  
  bool get isLoading => _isLoading.value;
  List<String> get data => _data;
  
  @override
  void onInit() {
    super.onInit();
    loadData();
  }
  
  Future<void> loadData() async {
    _isLoading.value = true;
    try {
      // 数据加载逻辑
      final result = await repository.fetchData();
      _data.assignAll(result);
    } catch (e) {
      Get.snackbar('错误', '数据加载失败');
    } finally {
      _isLoading.value = false;
    }
  }
}

// View示例
class HomePage extends GetView<HomeController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('首页')),
      body: Obx(() {
        if (controller.isLoading) {
          return Center(child: CircularProgressIndicator());
        }
        return ListView.builder(
          itemCount: controller.data.length,
          itemBuilder: (context, index) {
            return ListTile(
              title: Text(controller.data[index]),
            );
          },
        );
      }),
    );
  }
}

// Binding示例
class HomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<HomeController>(() => HomeController());
  }
}
```

### 阶段三：优化与测试

#### 3.1 性能优化
```dart
// 使用const构造函数
class MyWidget extends StatelessWidget {
  const MyWidget({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return const Text('Hello World');
  }
}

// 避免不必要的重建
class OptimizedWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Obx(() => Text(controller.text.value));
  }
}

// 使用ListView.builder进行懒加载
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return ItemWidget(item: items[index]);
  },
)
```

#### 3.2 错误处理
```dart
// 全局错误处理
class ErrorHandler {
  static void handleError(dynamic error) {
    if (error is NetworkException) {
      Get.snackbar('网络错误', '请检查网络连接');
    } else if (error is ValidationException) {
      Get.snackbar('输入错误', error.message);
    } else {
      Get.snackbar('系统错误', '操作失败，请重试');
    }
  }
}

// 异步操作错误处理
Future<void> performAction() async {
  try {
    await someAsyncOperation();
  } catch (e) {
    ErrorHandler.handleError(e);
  }
}
```

#### 3.3 测试策略
```dart
// 单元测试
void main() {
  group('HomeController Tests', () {
    late HomeController controller;
    
    setUp(() {
      controller = HomeController();
    });
    
    test('should load data successfully', () async {
      await controller.loadData();
      expect(controller.data.isNotEmpty, true);
      expect(controller.isLoading, false);
    });
  });
}

// Widget测试
void main() {
  testWidgets('HomePage should display data', (WidgetTester tester) async {
    await tester.pumpWidget(MyApp());
    await tester.pumpAndSettle();
    
    expect(find.text('首页'), findsOneWidget);
    expect(find.byType(ListView), findsOneWidget);
  });
}
```

### 阶段四：部署与发布

#### 4.1 构建配置
```yaml
# pubspec.yaml
flutter:
  assets:
    - assets/images/
    - assets/icons/
  
# Android配置
android {
    compileSdkVersion 34
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
    }
}
```

#### 4.2 发布流程
```bash
# Android发布
flutter build apk --release
flutter build appbundle --release

# iOS发布
flutter build ios --release
```
</process>

<criteria>
## Flutter开发质量标准

### 代码质量
- ✅ 遵循Dart/Flutter代码规范
- ✅ 使用空安全特性
- ✅ 合理的注释和文档
- ✅ 无编译警告和错误

### 性能标准
- ✅ 应用启动时间 < 3秒
- ✅ 页面切换流畅度 60fps
- ✅ 内存使用合理，无内存泄漏
- ✅ APK/IPA大小控制在合理范围

### 用户体验
- ✅ 响应式设计，适配不同屏幕
- ✅ 加载状态和错误处理完善
- ✅ 交互反馈及时准确
- ✅ 无崩溃和ANR问题

### 架构质量
- ✅ 模块化设计，职责清晰
- ✅ 状态管理规范统一
- ✅ 依赖注入合理使用
- ✅ 代码可测试性良好
</criteria>
</reference>