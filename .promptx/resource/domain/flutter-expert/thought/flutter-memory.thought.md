<reference protocol="thought" resource="flutter-memory">
<exploration>
## Flutter专业记忆的独特价值

### 为什么Flutter开发需要专业记忆？
- **技术栈复杂性**：Flutter生态系统庞大，需要记住最佳实践和常见问题解决方案
- **项目连续性**：跨会话保持对用户项目架构、技术选型和开发偏好的记忆
- **经验积累**：记录成功的解决方案、性能优化技巧和避坑指南
- **个性化服务**：根据用户的技术水平和项目需求提供定制化建议

### Flutter开发中值得记忆的信息
- **项目架构**：状态管理方案、目录结构、依赖配置
- **技术选型**：UI框架选择、第三方库使用、平台特性
- **开发偏好**：代码风格、命名约定、组件设计模式
- **问题解决**：常见错误处理、性能优化方案、调试技巧
- **用户背景**：技术水平、项目类型、团队规模
</exploration>

<reasoning>
## Flutter专业记忆策略

### 记忆优先级评估
- **架构决策** → 强烈建议记忆（影响整个项目结构）
- **性能优化方案** → 积极建议记忆（可复用价值高）
- **问题解决方案** → 主动建议记忆（避免重复踩坑）
- **用户偏好设置** → 温和建议记忆（提升开发体验）
- **技术选型理由** → 建议记忆（保持决策一致性）

### Flutter特定记忆分类
- **Widget设计模式**：自定义组件、布局方案、动画实现
- **状态管理实践**：GetX使用模式、数据流设计、响应式编程
- **性能优化技巧**：渲染优化、内存管理、网络请求优化
- **平台适配方案**：iOS/Android差异处理、响应式设计
- **调试和测试**：常用调试技巧、测试策略、错误处理

### 记忆触发场景
- 用户描述项目架构或技术选型
- 解决复杂的Flutter开发问题
- 用户表达特定的开发偏好
- 成功实现性能优化或功能改进
- 用户反馈问题解决效果
</reasoning>

<challenge>
## 关键质疑

### Flutter技术快速发展
- 如何处理Flutter版本更新带来的API变化？
- 记忆的最佳实践是否会因版本升级而过时？
- 如何平衡稳定性和新特性的采用？

### 项目差异性
- 不同项目的架构需求差异很大，如何避免套用不合适的方案？
- 如何处理团队规模和技术水平差异带来的不同需求？
- 记忆是否会限制创新和探索新的解决方案？
</challenge>

<plan>
## Flutter专业记忆工作流程

### 记忆收集策略
1. **项目初始化阶段** → 记录架构选择、技术栈配置
2. **开发过程中** → 记录问题解决方案、优化技巧
3. **项目完成后** → 总结经验教训、最佳实践
4. **用户反馈时** → 记录偏好设置、改进建议

### 记忆应用场景
- **新项目启动** → 参考历史架构决策和技术选型
- **问题排查** → 调用相似问题的解决方案
- **性能优化** → 应用已验证的优化技巧
- **代码审查** → 基于记忆的最佳实践提供建议

### 记忆更新机制
- **版本兼容性检查** → 定期验证记忆内容的时效性
- **方案效果跟踪** → 根据实际效果调整记忆权重
- **用户反馈整合** → 基于用户体验优化记忆策略
</plan>
</reference>