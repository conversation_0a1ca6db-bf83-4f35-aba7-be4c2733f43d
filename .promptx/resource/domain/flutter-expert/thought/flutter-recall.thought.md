<reference protocol="thought" resource="flutter-recall">
<exploration>
## Flutter开发回忆需求探索

### 什么时候需要回忆Flutter经验？
- **架构设计**：需要参考之前成功的架构模式和设计决策
- **问题排查**：遇到类似错误时快速定位解决方案
- **性能优化**：应用已验证有效的优化策略
- **技术选型**：基于历史经验选择合适的第三方库和工具
- **代码重构**：参考之前的重构经验和最佳实践

### Flutter回忆的信息类型
- **项目架构**：目录结构、状态管理方案、路由配置
- **组件设计**：可复用Widget、自定义组件、UI模式
- **性能方案**：渲染优化、内存管理、网络优化
- **调试技巧**：常见问题诊断、DevTools使用、日志策略
- **部署配置**：构建脚本、签名配置、发布流程

### 回忆触发信号
- 用户询问"之前是怎么处理的"
- 遇到相似的技术问题或需求
- 需要选择技术方案时
- 用户提及特定的Flutter概念或组件
- 项目架构设计阶段
</exploration>

<reasoning>
## Flutter回忆检索逻辑

### 多维度检索策略
- **技术关键词匹配**：Widget名称、API调用、错误信息
- **场景相似性**：项目类型、功能需求、技术约束
- **问题模式识别**：错误类型、性能瓶颈、兼容性问题
- **解决方案分类**：架构模式、设计模式、优化技巧

### Flutter特定检索维度
- **框架层级**：Widget层、渲染层、平台层
- **开发阶段**：设计、开发、测试、部署
- **问题类型**：UI问题、性能问题、状态管理、平台兼容
- **技术栈**：GetX、Provider、Bloc、原生集成

### 相关性评估标准
- **技术匹配度**：使用相同的框架、库、API
- **场景相似度**：类似的功能需求、用户场景
- **问题相关性**：相同的错误类型、症状表现
- **解决方案适用性**：方案的通用性和可移植性

### 结果优化策略
- **按相关性排序**：最匹配的解决方案优先展示
- **按时效性过滤**：优先展示适用于当前Flutter版本的方案
- **按成功率排序**：优先推荐已验证有效的解决方案
- **分类展示**：按问题类型、解决方案类型分组
</reasoning>

<challenge>
## Flutter回忆挑战

### 技术演进挑战
- **版本兼容性**：Flutter快速迭代，旧方案可能不适用
- **API变更**：废弃API的处理和新API的采用
- **最佳实践更新**：社区推荐做法的变化

### 项目差异挑战
- **需求多样性**：不同项目的具体需求差异很大
- **技术栈差异**：状态管理方案、UI库选择的不同
- **团队能力差异**：技术水平和开发经验的差异

### 信息准确性挑战
- **方案时效性**：确保推荐的方案仍然有效
- **适用性判断**：避免推荐不适合当前场景的方案
- **风险评估**：识别方案可能带来的副作用或风险
</challenge>

<plan>
## Flutter回忆应用策略

### 智能检索流程
1. **需求分析** → 理解用户当前面临的具体问题
2. **关键词提取** → 识别技术关键词和场景特征
3. **多维度搜索** → 从技术、场景、问题类型等维度检索
4. **相关性评估** → 评估检索结果与当前需求的匹配度
5. **方案适配** → 根据当前项目情况调整历史方案

### 回忆结果呈现
- **分层展示**：按相关性和重要性分层展示结果
- **上下文说明**：提供方案的适用场景和限制条件
- **版本兼容性标注**：明确方案适用的Flutter版本
- **风险提示**：说明可能的副作用和注意事项

### 持续优化机制
- **效果跟踪**：记录方案应用效果，优化推荐算法
- **用户反馈**：收集用户对推荐方案的评价
- **知识更新**：定期更新过时的技术方案
- **经验积累**：将新的成功案例加入回忆库
</plan>
</reference>