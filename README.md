# nicexfer_app

Nicexfer 是一款融合了现代极简主义风格的文件传输工具，一个围绕文件交换行为构建起来的轻社交工具平台。

## ✅ 核心理念总结：

🧩 「任务 = 一种文件链接的行为」
• 发送链接任务：给别人发文件
• 收集链接任务：请求别人上传
• 分享链接任务：将文件公开/群体可访问
• 协作链接任务：多人共享收集 / 多人管理（可后期扩展）
• ✅ 所有这些，都是围绕一个链接、一个文件行为形成的“轻社交关系”

## 💡 App 的产品定位价值：

### 一个围绕文件交换行为构建起来的轻社交工具平台

    •   “每个链接，都是一个故事”
    •   “每个传输，都是一种互动”
    •   类似 Notion link / figma share / google doc 分享的社交感知感
    •   可以往评论、下载状态、查看者身份、点赞、转发 等方向扩展
    •   可以在某个“链接”上，进行评论、点赞、转发等互动
    •   可以在某个“盒子”上，进行聊天对话
    •   可以在某个“盒子”上，进行文件管理

## 功能特性

```plaintext
- 极速文件传输：利用先进的传输技术，实现快速、稳定的文件传输。
- 多平台支持：支持多种操作系统，包括 Windows、macOS、Linux 和 Android。
- 隐私保护：采用加密传输，确保文件传输的安全性。
- 多文件传输：支持同时传输多个文件，提高传输效率。
- 多设备同步：支持多设备之间的文件同步，方便文件管理。
```

## 产品架构

```plaintext
Nicexfer 的产品架构采用了现代化的设计理念，包括以下关键组件：

- 前端：采用 Flutter 框架和 GetX 状态管理插件，提供用户界面和交互功能。
- 后端：采用 Supabase 作为后端服务器，处理文件传输和同步逻辑。
  - 身份认证采用 Supabase 的 Auth（身份）
  - 数据存储采用 Supabase 的 Database（文件/互动/评论等表）
  - 文件存储采用 Supabase 的 Storage（文件管理）
  - 消息推送采用 Supabase 的 Edge Functions（通知、分享链接、分享图生成等）
  - 实时通信采用 Supabase 的 Realtime（订阅消息）
- 加密：采用 AES 算法进行文件加密，确保数据的安全性。
- 网络通信：采用 WebSocket 协议进行实时通信。
```

## 前端设计

```plaintext
Nicexfer 的前端采用 Flutter 框架，提供了现代化的用户界面和交互功能。以下是前端设计的关键要点：

- 界面风格：采用 Material 3 设计语言，采用类 iOS 玻璃拟态设计风格，主色调为 MaterialApp 最佳实践的颜色。
- 导航栏：采用底部导航栏，支持多页面切换。
- 页面布局：采用 GridView、ListView 布局，支持多文件传输。
- 交互元素：采用 Material 3 设计语言，提供丰富的交互元素。
- 动画效果：采用 animate_do 动画效果，提供流畅的用户体验。
```

## 前端路由规划

Nicexfer 的前端路由规划采用了以下路径：

```plaintext
导航条 ： [仪表板] [发送] [收集] [记录] [文件] 。
```

## 项目风味初始化

```bash
fvm flutter pub run flutter_flavorizr
```

## 目录结构架构规范

```plaintext
我使用的是 Clean Architecture 风格 Flutter 项目结构，每个模块都在 presentation/modules/ 下，包含 bindings/controllers/views/models/widgets/repositories/services 等文件夹。全局基础设施放在 core/ 目录下，包括路由、依赖注入、主题、国际化、工具类等。
```

## 项目目录结构

```plaintext
lib/
├── core/                     # 应用核心基础设施（全局工具 & 通用能力）
│   ├── constants/           # 静态常量、配置项（如 App 名称、颜色、枚举、路径）
│   ├── controllers/         # 全局控制器（如 Theme、语言、主导航等）
│   ├── di/                  # 依赖注入初始化，如 Get.put()、get_it.register()
│   ├── middlewares/         # 路由中间件（如权限判断、拦截器）
│   ├── routes/              # 全局路由配置（AppPages, AppRoutes）
│   ├── theme/               # 全局主题管理（样式、颜色、字体、亮暗模式）
│   ├── translations/        # 多语言配置（ar.dart, en.dart 等）
│   ├── utils/               # 全局工具类（如扩展、GetX helper 等）
│   └── infrastructure/      # 核心数据层（全局模型、服务、仓库）
│       ├── models/          # 共享数据模型（User、File、Task）
│       ├── repositories/    # 全局仓库接口或实现（如用户仓库）
│       └── services/        # 第三方服务封装（如 API 请求、Storage、通知）
│
├── presentation/            # UI 展现层（模块化、解耦、页面 & 组件）
│   ├── global_widgets/      # 全局复用组件（如按钮、表单、布局）
│   ├── shared/              # 可跨模块复用的 UI 组件（如 Dialog、BottomSheet）
│   └── modules/             # 每个功能模块（模块内可自包含 MVC 架构）
│       └── files/
│           ├── bindings/         # 模块依赖注入绑定（控制器注册等）
│           ├── controllers/      # 模块状态控制器（GetX Controller）
│           ├── models/           # 模块特有数据模型
│           ├── repositories/     # 模块级仓库（可依赖全局 Repository）
│           ├── services/         # 模块内部的逻辑服务（通常 UI 层服务）
│           ├── views/            # 页面 UI（Page、Scaffold 视图）
│           └── widgets/          # 模块特有组件（Card、Item、对话框等）
│
├── app.dart                  # 启动应用主配置（GetMaterialApp、theme、路由等）
├── flavors.dart              # 多环境配置入口（dev、prod）
└── main.dart                 # 启动入口，调用 runApp()
```

## 模块内部结构标准

```plaintext
modules/your_module/
├── bindings/         # Get.lazyPut() 注册 Controller / Service
├── controllers/      # 使用 GetX Controller 管理状态和逻辑
├── models/           # 模块专属模型（如临时表单、视图模型）
├── repositories/     # 封装业务数据操作（接口调用、状态处理）
├── services/         # 可选，用于处理模块业务逻辑（如文件排序）
├── views/            # 页面文件（xxx_page.dart）
└── widgets/          # 模块下的组件（如卡片、表单等）
```

## app 颜色系列

所有颜色都通过 theme_helper.dart 统一管理，便于维护和修改，要适配深色模式

## 配色方案

```bash
http://material-foundation.github.io/material-theme-builder/?primary=%2397ADE8&bodyFont=Noto+Sans+SC&displayFont=Noto+Sans+SC&colorMatch=true
```
