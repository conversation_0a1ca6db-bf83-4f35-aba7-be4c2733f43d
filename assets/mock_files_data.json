{"rootFiles": [{"id": "1", "name": "我的文档", "source": "文档", "date": "2024-12-15T10:30:00.000Z", "isFolder": true}, {"id": "2", "name": "项目开发", "source": "开发", "date": "2024-12-14T16:45:00.000Z", "isFolder": true}, {"id": "3", "name": "媒体资源", "source": "媒体", "date": "2024-12-13T14:20:00.000Z", "isFolder": true}, {"id": "4", "name": "学习资料", "source": "学习", "date": "2024-12-12T09:15:00.000Z", "isFolder": true}, {"id": "5", "name": "工作备份", "source": "备份", "date": "2024-12-11T18:30:00.000Z", "isFolder": true}, {"id": "101", "name": "重要通知.pdf", "source": "PDF", "date": "2024-12-15T11:20:00.000Z", "isFolder": false, "size": 2048576, "icon": "pdf"}, {"id": "102", "name": "会议纪要.docx", "source": "Word", "date": "2024-12-14T15:45:00.000Z", "isFolder": false, "size": 1536000, "icon": "document"}, {"id": "103", "name": "数据统计.xlsx", "source": "Excel", "date": "2024-12-13T13:30:00.000Z", "isFolder": false, "size": 3145728, "icon": "spreadsheet"}, {"id": "104", "name": "团队合影.jpg", "source": "JPG", "date": "2024-12-15T09:45:00.000Z", "isFolder": false, "size": 4194304, "icon": "image", "thumbnailUrl": "https://picsum.photos/200/200?random=7"}, {"id": "105", "name": "公司logo.png", "source": "PNG", "date": "2024-12-14T12:30:00.000Z", "isFolder": false, "size": 1048576, "icon": "image", "thumbnailUrl": "https://picsum.photos/200/200?random=8"}, {"id": "106", "name": "产品截图.png", "source": "PNG", "date": "2024-12-13T16:15:00.000Z", "isFolder": false, "size": 2621440, "icon": "image", "thumbnailUrl": "https://picsum.photos/200/200?random=9"}, {"id": "107", "name": "重要文档.pdf", "source": "PDF", "date": "2024-12-15T14:20:00.000Z", "isFolder": false, "size": 1572864, "icon": "pdf"}, {"id": "108", "name": "项目报告.docx", "source": "Word", "date": "2024-12-14T10:30:00.000Z", "isFolder": false, "size": 2097152, "icon": "document"}, {"id": "109", "name": "会议文档.txt", "source": "TXT", "date": "2024-12-13T09:15:00.000Z", "isFolder": false, "size": 524288, "icon": "document"}, {"id": "110", "name": "技术文档.md", "source": "<PERSON><PERSON>", "date": "2024-12-12T15:45:00.000Z", "isFolder": false, "size": 1048576, "icon": "document"}], "folderContents": {"1": [{"id": "1001", "name": "合同文件", "source": "合同", "date": "2024-12-10T10:00:00.000Z", "isFolder": true}, {"id": "1002", "name": "财务报表", "source": "财务", "date": "2024-12-09T14:30:00.000Z", "isFolder": true}, {"id": "1003", "name": "人事档案", "source": "人事", "date": "2024-12-08T16:15:00.000Z", "isFolder": true}, {"id": "1101", "name": "公司章程.pdf", "source": "PDF", "date": "2024-12-15T09:30:00.000Z", "isFolder": false, "size": 5242880, "icon": "pdf"}, {"id": "1102", "name": "员工手册.docx", "source": "Word", "date": "2024-12-14T11:45:00.000Z", "isFolder": false, "size": 2097152, "icon": "document"}], "2": [{"id": "2001", "name": "Flutter项目", "source": "Flutter", "date": "2024-12-14T16:45:00.000Z", "isFolder": true}, {"id": "2002", "name": "React项目", "source": "React", "date": "2024-12-13T10:20:00.000Z", "isFolder": true}, {"id": "2003", "name": "Node.js后端", "source": "Node.js", "date": "2024-12-12T14:30:00.000Z", "isFolder": true}, {"id": "2004", "name": "数据库设计", "source": "数据库", "date": "2024-12-11T09:15:00.000Z", "isFolder": true}, {"id": "2101", "name": "项目需求文档.md", "source": "<PERSON><PERSON>", "date": "2024-12-15T08:30:00.000Z", "isFolder": false, "size": 65536, "icon": "text"}, {"id": "2102", "name": "API接口文档.json", "source": "JSON", "date": "2024-12-14T12:15:00.000Z", "isFolder": false, "size": 32768, "icon": "code"}], "3": [{"id": "3001", "name": "图片素材", "source": "图片", "date": "2024-12-13T14:20:00.000Z", "isFolder": true}, {"id": "3002", "name": "视频资源", "source": "视频", "date": "2024-12-12T16:30:00.000Z", "isFolder": true}, {"id": "3003", "name": "音频文件", "source": "音频", "date": "2024-12-11T11:45:00.000Z", "isFolder": true}, {"id": "3004", "name": "设计稿", "source": "设计", "date": "2024-12-10T13:20:00.000Z", "isFolder": true}, {"id": "3101", "name": "产品宣传片.mp4", "source": "视频", "date": "2024-12-15T14:30:00.000Z", "isFolder": false, "size": 104857600, "icon": "video"}], "4": [{"id": "4001", "name": "编程教程", "source": "教程", "date": "2024-12-12T09:15:00.000Z", "isFolder": true}, {"id": "4002", "name": "技术文档", "source": "技术", "date": "2024-12-11T15:30:00.000Z", "isFolder": true}, {"id": "4003", "name": "在线课程", "source": "课程", "date": "2024-12-10T10:45:00.000Z", "isFolder": true}, {"id": "4101", "name": "Flutter开发指南.pdf", "source": "PDF", "date": "2024-12-15T16:20:00.000Z", "isFolder": false, "size": 15728640, "icon": "pdf"}, {"id": "4102", "name": "Dart语言手册.epub", "source": "电子书", "date": "2024-12-14T13:45:00.000Z", "isFolder": false, "size": 8388608, "icon": "document"}], "5": [{"id": "5001", "name": "2024年备份", "source": "备份", "date": "2024-12-11T18:30:00.000Z", "isFolder": true}, {"id": "5002", "name": "系统配置", "source": "配置", "date": "2024-12-10T12:15:00.000Z", "isFolder": true}, {"id": "5101", "name": "数据库备份.sql", "source": "SQL", "date": "2024-12-15T20:00:00.000Z", "isFolder": false, "size": 52428800, "icon": "code"}], "1001": [{"id": "10011", "name": "销售合同", "source": "销售", "date": "2024-12-10T10:00:00.000Z", "isFolder": true}, {"id": "10012", "name": "采购合同", "source": "采购", "date": "2024-12-09T14:30:00.000Z", "isFolder": true}, {"id": "10013", "name": "服务协议", "source": "服务", "date": "2024-12-08T16:15:00.000Z", "isFolder": true}, {"id": "100111", "name": "保密协议模板.docx", "source": "Word", "date": "2024-12-15T10:30:00.000Z", "isFolder": false, "size": 1048576, "icon": "document"}], "2001": [{"id": "20011", "name": "移动应用", "source": "移动端", "date": "2024-12-14T16:45:00.000Z", "isFolder": true}, {"id": "20012", "name": "Web应用", "source": "Web端", "date": "2024-12-13T11:20:00.000Z", "isFolder": true}, {"id": "20013", "name": "桌面应用", "source": "桌面端", "date": "2024-12-12T15:30:00.000Z", "isFolder": true}, {"id": "200111", "name": "pubspec.yaml", "source": "YAML", "date": "2024-12-15T17:20:00.000Z", "isFolder": false, "size": 4096, "icon": "code"}, {"id": "200112", "name": "main.dart", "source": "Dart", "date": "2024-12-15T16:45:00.000Z", "isFolder": false, "size": 8192, "icon": "code"}], "3001": [{"id": "30011", "name": "产品图片", "source": "产品", "date": "2024-12-13T14:20:00.000Z", "isFolder": true}, {"id": "30012", "name": "用户头像", "source": "头像", "date": "2024-12-12T10:30:00.000Z", "isFolder": true}, {"id": "30013", "name": "背景图片", "source": "背景", "date": "2024-12-11T16:45:00.000Z", "isFolder": true}, {"id": "30014", "name": "图标素材", "source": "图标", "date": "2024-12-10T12:15:00.000Z", "isFolder": true}, {"id": "300111", "name": "logo.png", "source": "PNG", "date": "2024-12-15T13:30:00.000Z", "isFolder": false, "size": 524288, "icon": "image", "thumbnailUrl": "https://picsum.photos/200/200?random=1"}, {"id": "300112", "name": "banner.jpg", "source": "JPG", "date": "2024-12-14T10:15:00.000Z", "isFolder": false, "size": 1048576, "icon": "image", "thumbnailUrl": "https://picsum.photos/200/200?random=2"}, {"id": "300113", "name": "product_showcase.png", "source": "PNG", "date": "2024-12-13T16:20:00.000Z", "isFolder": false, "size": 2097152, "icon": "image", "thumbnailUrl": "https://picsum.photos/200/200?random=3"}], "4001": [{"id": "40011", "name": "Flutter教程", "source": "Flutter", "date": "2024-12-12T09:15:00.000Z", "isFolder": true}, {"id": "40012", "name": "React教程", "source": "React", "date": "2024-12-11T14:30:00.000Z", "isFolder": true}, {"id": "40013", "name": "Python教程", "source": "Python", "date": "2024-12-10T11:45:00.000Z", "isFolder": true}, {"id": "400111", "name": "基础语法.md", "source": "<PERSON><PERSON>", "date": "2024-12-15T15:20:00.000Z", "isFolder": false, "size": 16384, "icon": "text"}], "5001": [{"id": "50011", "name": "第一季度", "source": "Q1", "date": "2024-03-31T23:59:00.000Z", "isFolder": true}, {"id": "50012", "name": "第二季度", "source": "Q2", "date": "2024-06-30T23:59:00.000Z", "isFolder": true}, {"id": "50013", "name": "第三季度", "source": "Q3", "date": "2024-09-30T23:59:00.000Z", "isFolder": true}, {"id": "50014", "name": "第四季度", "source": "Q4", "date": "2024-12-11T18:30:00.000Z", "isFolder": true}], "20011": [{"id": "200111", "name": "Android项目", "source": "Android", "date": "2024-12-14T16:45:00.000Z", "isFolder": true}, {"id": "200112", "name": "iOS项目", "source": "iOS", "date": "2024-12-13T12:30:00.000Z", "isFolder": true}, {"id": "200113", "name": "跨平台项目", "source": "跨平台", "date": "2024-12-12T14:15:00.000Z", "isFolder": true}, {"id": "2001111", "name": "app.dart", "source": "Dart", "date": "2024-12-15T18:30:00.000Z", "isFolder": false, "size": 12288, "icon": "code"}], "30011": [{"id": "300111", "name": "手机产品", "source": "手机", "date": "2024-12-13T14:20:00.000Z", "isFolder": true}, {"id": "300112", "name": "电脑产品", "source": "电脑", "date": "2024-12-12T11:30:00.000Z", "isFolder": true}, {"id": "300113", "name": "配件产品", "source": "配件", "date": "2024-12-11T15:45:00.000Z", "isFolder": true}, {"id": "3001111", "name": "产品详情图.jpg", "source": "JPG", "date": "2024-12-15T12:45:00.000Z", "isFolder": false, "size": 2097152, "icon": "image", "thumbnailUrl": "https://picsum.photos/200/200?random=4"}, {"id": "3001112", "name": "产品展示图.png", "source": "PNG", "date": "2024-12-14T14:30:00.000Z", "isFolder": false, "size": 1536000, "icon": "image", "thumbnailUrl": "https://picsum.photos/200/200?random=5"}, {"id": "3001113", "name": "产品特写.jpg", "source": "JPG", "date": "2024-12-13T11:15:00.000Z", "isFolder": false, "size": 3145728, "icon": "image", "thumbnailUrl": "https://picsum.photos/200/200?random=6"}], "40011": [{"id": "400111", "name": "基础入门", "source": "入门", "date": "2024-12-12T09:15:00.000Z", "isFolder": true}, {"id": "400112", "name": "进阶开发", "source": "进阶", "date": "2024-12-11T13:30:00.000Z", "isFolder": true}, {"id": "400113", "name": "高级技巧", "source": "高级", "date": "2024-12-10T16:45:00.000Z", "isFolder": true}, {"id": "4001111", "name": "环境搭建.md", "source": "<PERSON><PERSON>", "date": "2024-12-15T14:20:00.000Z", "isFolder": false, "size": 32768, "icon": "text"}], "50011": [{"id": "500111", "name": "一月份", "source": "1月", "date": "2024-01-31T23:59:00.000Z", "isFolder": true}, {"id": "500112", "name": "二月份", "source": "2月", "date": "2024-02-29T23:59:00.000Z", "isFolder": true}, {"id": "500113", "name": "三月份", "source": "3月", "date": "2024-03-31T23:59:00.000Z", "isFolder": true}, {"id": "5001111", "name": "Q1总结报告.pdf", "source": "PDF", "date": "2024-03-31T20:00:00.000Z", "isFolder": false, "size": 10485760, "icon": "pdf"}], "200111": [{"id": "2001111", "name": "原生开发", "source": "原生", "date": "2024-12-14T16:45:00.000Z", "isFolder": true}, {"id": "2001112", "name": "混合开发", "source": "混合", "date": "2024-12-13T10:20:00.000Z", "isFolder": true}, {"id": "20011111", "name": "MainActivity.java", "source": "Java", "date": "2024-12-15T19:15:00.000Z", "isFolder": false, "size": 16384, "icon": "code"}], "300111": [{"id": "3001111", "name": "旗舰机型", "source": "旗舰", "date": "2024-12-13T14:20:00.000Z", "isFolder": true}, {"id": "3001112", "name": "中端机型", "source": "中端", "date": "2024-12-12T12:30:00.000Z", "isFolder": true}, {"id": "3001113", "name": "入门机型", "source": "入门", "date": "2024-12-11T16:45:00.000Z", "isFolder": true}, {"id": "30011111", "name": "产品规格表.xlsx", "source": "Excel", "date": "2024-12-15T11:30:00.000Z", "isFolder": false, "size": 1048576, "icon": "spreadsheet"}], "400111": [{"id": "4001111", "name": "第一章", "source": "章节", "date": "2024-12-12T09:15:00.000Z", "isFolder": true}, {"id": "4001112", "name": "第二章", "source": "章节", "date": "2024-12-11T14:30:00.000Z", "isFolder": true}, {"id": "4001113", "name": "第三章", "source": "章节", "date": "2024-12-10T16:45:00.000Z", "isFolder": true}, {"id": "40011111", "name": "课程大纲.pdf", "source": "PDF", "date": "2024-12-15T13:20:00.000Z", "isFolder": false, "size": 4194304, "icon": "pdf"}], "500111": [{"id": "5001111", "name": "第一周", "source": "周", "date": "2024-01-07T23:59:00.000Z", "isFolder": true}, {"id": "5001112", "name": "第二周", "source": "周", "date": "2024-01-14T23:59:00.000Z", "isFolder": true}, {"id": "5001113", "name": "第三周", "source": "周", "date": "2024-01-21T23:59:00.000Z", "isFolder": true}, {"id": "5001114", "name": "第四周", "source": "周", "date": "2024-01-28T23:59:00.000Z", "isFolder": true}, {"id": "50011111", "name": "一月工作总结.docx", "source": "Word", "date": "2024-01-31T18:00:00.000Z", "isFolder": false, "size": 2097152, "icon": "document"}], "5001111": [{"id": "50011111", "name": "周一", "source": "日", "date": "2024-01-01T23:59:00.000Z", "isFolder": true}, {"id": "50011112", "name": "周二", "source": "日", "date": "2024-01-02T23:59:00.000Z", "isFolder": true}, {"id": "50011113", "name": "周三", "source": "日", "date": "2024-01-03T23:59:00.000Z", "isFolder": true}, {"id": "50011114", "name": "周四", "source": "日", "date": "2024-01-04T23:59:00.000Z", "isFolder": true}, {"id": "50011115", "name": "周五", "source": "日", "date": "2024-01-05T23:59:00.000Z", "isFolder": true}, {"id": "500111111", "name": "第一周工作日志.txt", "source": "文本", "date": "2024-01-07T17:30:00.000Z", "isFolder": false, "size": 8192, "icon": "text"}], "50011111": [{"id": "500111111", "name": "上午工作", "source": "时段", "date": "2024-01-01T12:00:00.000Z", "isFolder": true}, {"id": "500111112", "name": "下午工作", "source": "时段", "date": "2024-01-01T18:00:00.000Z", "isFolder": true}, {"id": "5001111111", "name": "每日总结.md", "source": "<PERSON><PERSON>", "date": "2024-01-01T20:00:00.000Z", "isFolder": false, "size": 4096, "icon": "text"}]}}